{"ast": null, "code": "/*!\n * @kurkle/color v0.3.4\n * https://github.com/kurkle/color#readme\n * (c) 2024 <PERSON><PERSON>\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\nconst map$1 = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15,\n  a: 10,\n  b: 11,\n  c: 12,\n  d: 13,\n  e: 14,\n  f: 15\n};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => (b & 0xF0) >> 4 === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? map$1[str[7]] << 4 | map$1[str[8]] : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f) : undefined;\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return (g - b) / d + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (Array.isArray(a) ? f(a[0], a[1], a[2]) : f(a, b, c)).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255 ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})` : `hsl(${h}, ${s}%, ${l}%)`;\n}\nconst map = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names$1 = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (v.a < 255 ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})` : `rgb(${v.r}, ${v.g}, ${v.b})`);\n}\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 255\n  };\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {\n        r: input[0],\n        g: input[1],\n        b: input[2],\n        a: 255\n      };\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    });\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\nfunction index_esm(input) {\n  return new Color(input);\n}\nexport { Color, b2n, b2p, index_esm as default, hexParse, hexString, hsl2rgb, hslString, hsv2rgb, hueParse, hwb2rgb, lim, n2b, n2p, nameParse, p2b, rgb2hsl, rgbParse, rgbString, rotate, round };", "map": {"version": 3, "names": ["round", "v", "lim", "l", "h", "Math", "max", "min", "p2b", "b2p", "n2b", "b2n", "n2p", "map$1", "A", "B", "C", "D", "E", "F", "a", "b", "c", "d", "e", "f", "hex", "h1", "h2", "eq", "isShort", "r", "g", "hexParse", "str", "len", "length", "ret", "alpha", "hexString", "undefined", "HUE_RE", "hsl2rgbn", "s", "n", "k", "hsv2rgbn", "hwb2rgbn", "w", "rgb", "i", "hueValue", "rgb2hsl", "range", "calln", "Array", "isArray", "map", "hsl2rgb", "hwb2rgb", "hsv2rgb", "hue", "hue<PERSON><PERSON><PERSON>", "m", "exec", "p1", "p2", "rotate", "deg", "hslString", "x", "Z", "Y", "X", "W", "V", "U", "T", "S", "R", "Q", "P", "O", "N", "M", "L", "K", "G", "H", "I", "J", "names$1", "OiceXe", "antiquewEte", "aqua", "aquamarRe", "azuY", "beige", "bisque", "black", "blan<PERSON>ed<PERSON><PERSON>", "Xe", "XeviTet", "bPwn", "burlywood", "caMtXe", "<PERSON><PERSON><PERSON><PERSON>", "KocTate", "cSO", "cSnflowerXe", "cSnsilk", "crimson", "cyan", "xXe", "xcyan", "xgTMnPd", "xWay", "xgYF", "xgYy", "xkhaki", "xmagFta", "xTivegYF", "xSange", "xScEd", "xYd", "xsOmon", "xsHgYF", "xUXe", "xUWay", "xUgYy", "xQe", "xviTet", "dAppRk", "dApskyXe", "dim<PERSON>ay", "dimgYy", "dodgerXe", "fiYbrick", "flSOwEte", "foYstWAn", "fuKsia", "gaRsbSo", "ghostwEte", "gTd", "gTMnPd", "Way", "gYF", "gYFLw", "gYy", "honeyMw", "hotpRk", "RdianYd", "Rdigo", "ivSy", "khaki", "lavFMr", "lavFMrXsh", "lawngYF", "NmoncEffon", "ZXe", "ZcSO", "<PERSON><PERSON><PERSON>", "ZgTMnPdLw", "ZWay", "ZgYF", "ZgYy", "ZpRk", "ZsOmon", "ZsHgYF", "ZskyXe", "ZUWay", "ZUgYy", "ZstAlXe", "ZLw", "lime", "limegYF", "lRF", "magFta", "ma<PERSON><PERSON>", "VaquamarRe", "VXe", "VScEd", "VpurpN", "VsHgYF", "VUXe", "VsprRggYF", "VQe", "VviTetYd", "midnightXe", "mRtcYam", "misty<PERSON>e", "moccasR", "navajowEte", "navy", "Tdlace", "Tive", "TivedBb", "<PERSON><PERSON>", "SangeYd", "ScEd", "pOegTMnPd", "pOegYF", "pOeQe", "pOeviTetYd", "papayawEp", "pHKpuff", "peru", "pRk", "plum", "powMrXe", "purpN", "YbeccapurpN", "Yd", "Psybrown", "PyOXe", "saddNbPwn", "sOmon", "sandybPwn", "sHgYF", "sHshell", "siFna", "silver", "skyXe", "UXe", "UWay", "UgYy", "snow", "sprRggYF", "stAlXe", "tan", "teO", "tEstN", "tomato", "Qe", "viTet", "JHt", "wEte", "wEtesmoke", "Lw", "LwgYF", "unpack", "unpacked", "keys", "Object", "tkeys", "j", "ok", "nk", "replace", "parseInt", "names", "nameParse", "transparent", "toLowerCase", "RGB_RE", "rgbParse", "rgbString", "to", "pow", "from", "interpolate", "rgb1", "rgb2", "t", "modHSL", "ratio", "tmp", "clone", "proto", "assign", "fromObject", "input", "functionParse", "char<PERSON>t", "Color", "constructor", "type", "_rgb", "_valid", "valid", "obj", "mix", "color", "weight", "c1", "c2", "w2", "p", "w1", "clearer", "greyscale", "val", "opaquer", "negate", "lighten", "darken", "saturate", "desaturate", "index_esm", "default"], "sources": ["C:/Users/<USER>/Desktop/freelance-marketplace setup/freelance-marketplace setup/client/node_modules/@kurkle/color/dist/color.esm.js"], "sourcesContent": ["/*!\n * @kurkle/color v0.3.4\n * https://github.com/kurkle/color#readme\n * (c) 2024 <PERSON><PERSON>\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names$1 = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\n\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\n\nfunction index_esm(input) {\n  return new Color(input);\n}\n\nexport { Color, b2n, b2p, index_esm as default, hexParse, hexString, hsl2rgb, hslString, hsv2rgb, hueParse, hwb2rgb, lim, n2b, n2p, nameParse, p2b, rgb2hsl, rgbParse, rgbString, rotate, round };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAOA,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AACA,MAAMC,GAAG,GAAGA,CAACD,CAAC,EAAEE,CAAC,EAAEC,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,CAAC,EAAEG,CAAC,CAAC,EAAED,CAAC,CAAC;AACpD,SAASK,GAAGA,CAACP,CAAC,EAAE;EACd,OAAOC,GAAG,CAACF,KAAK,CAACC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACrC;AACA,SAASQ,GAAGA,CAACR,CAAC,EAAE;EACd,OAAOC,GAAG,CAACF,KAAK,CAACC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACrC;AACA,SAASS,GAAGA,CAACT,CAAC,EAAE;EACd,OAAOC,GAAG,CAACF,KAAK,CAACC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpC;AACA,SAASU,GAAGA,CAACV,CAAC,EAAE;EACd,OAAOC,GAAG,CAACF,KAAK,CAACC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACzC;AACA,SAASW,GAAGA,CAACX,CAAC,EAAE;EACd,OAAOC,GAAG,CAACF,KAAK,CAACC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpC;AAEA,MAAMY,KAAK,GAAG;EAAC,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE;AAAE,CAAC;AAC9J,MAAMC,GAAG,GAAG,CAAC,GAAG,kBAAkB,CAAC;AACnC,MAAMC,EAAE,GAAGN,CAAC,IAAIK,GAAG,CAACL,CAAC,GAAG,GAAG,CAAC;AAC5B,MAAMO,EAAE,GAAGP,CAAC,IAAIK,GAAG,CAAC,CAACL,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAGK,GAAG,CAACL,CAAC,GAAG,GAAG,CAAC;AACnD,MAAMQ,EAAE,GAAGR,CAAC,IAAK,CAACA,CAAC,GAAG,IAAI,KAAK,CAAC,MAAOA,CAAC,GAAG,GAAG,CAAC;AAC/C,MAAMS,OAAO,GAAG7B,CAAC,IAAI4B,EAAE,CAAC5B,CAAC,CAAC8B,CAAC,CAAC,IAAIF,EAAE,CAAC5B,CAAC,CAAC+B,CAAC,CAAC,IAAIH,EAAE,CAAC5B,CAAC,CAACoB,CAAC,CAAC,IAAIQ,EAAE,CAAC5B,CAAC,CAACmB,CAAC,CAAC;AAC7D,SAASa,QAAQA,CAACC,GAAG,EAAE;EACrB,IAAIC,GAAG,GAAGD,GAAG,CAACE,MAAM;EACpB,IAAIC,GAAG;EACP,IAAIH,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,IAAIC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC1BE,GAAG,GAAG;QACJN,CAAC,EAAE,GAAG,GAAGlB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3BF,CAAC,EAAE,GAAG,GAAGnB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3Bb,CAAC,EAAE,GAAG,GAAGR,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3Bd,CAAC,EAAEe,GAAG,KAAK,CAAC,GAAGtB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG;MACtC,CAAC;IACH,CAAC,MAAM,IAAIC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MACjCE,GAAG,GAAG;QACJN,CAAC,EAAElB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCF,CAAC,EAAEnB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCb,CAAC,EAAER,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCd,CAAC,EAAEe,GAAG,KAAK,CAAC,GAAItB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI;MACxD,CAAC;IACH;EACF;EACA,OAAOG,GAAG;AACZ;AACA,MAAMC,KAAK,GAAGA,CAAClB,CAAC,EAAEK,CAAC,KAAKL,CAAC,GAAG,GAAG,GAAGK,CAAC,CAACL,CAAC,CAAC,GAAG,EAAE;AAC3C,SAASmB,SAASA,CAACtC,CAAC,EAAE;EACpB,IAAIwB,CAAC,GAAGK,OAAO,CAAC7B,CAAC,CAAC,GAAG0B,EAAE,GAAGC,EAAE;EAC5B,OAAO3B,CAAC,GACJ,GAAG,GAAGwB,CAAC,CAACxB,CAAC,CAAC8B,CAAC,CAAC,GAAGN,CAAC,CAACxB,CAAC,CAAC+B,CAAC,CAAC,GAAGP,CAAC,CAACxB,CAAC,CAACoB,CAAC,CAAC,GAAGiB,KAAK,CAACrC,CAAC,CAACmB,CAAC,EAAEK,CAAC,CAAC,GAC9Ce,SAAS;AACf;AAEA,MAAMC,MAAM,GAAG,8GAA8G;AAC7H,SAASC,QAAQA,CAACtC,CAAC,EAAEuC,CAAC,EAAExC,CAAC,EAAE;EACzB,MAAMiB,CAAC,GAAGuB,CAAC,GAAGtC,IAAI,CAACE,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAMsB,CAAC,GAAGA,CAACmB,CAAC,EAAEC,CAAC,GAAG,CAACD,CAAC,GAAGxC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAKD,CAAC,GAAGiB,CAAC,GAAGf,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACsC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvF,OAAO,CAACpB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,SAASqB,QAAQA,CAAC1C,CAAC,EAAEuC,CAAC,EAAE1C,CAAC,EAAE;EACzB,MAAMwB,CAAC,GAAGA,CAACmB,CAAC,EAAEC,CAAC,GAAG,CAACD,CAAC,GAAGxC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKH,CAAC,GAAGA,CAAC,GAAG0C,CAAC,GAAGtC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACsC,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,OAAO,CAACpB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,SAASsB,QAAQA,CAAC3C,CAAC,EAAE4C,CAAC,EAAE3B,CAAC,EAAE;EACzB,MAAM4B,GAAG,GAAGP,QAAQ,CAACtC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAC/B,IAAI8C,CAAC;EACL,IAAIF,CAAC,GAAG3B,CAAC,GAAG,CAAC,EAAE;IACb6B,CAAC,GAAG,CAAC,IAAIF,CAAC,GAAG3B,CAAC,CAAC;IACf2B,CAAC,IAAIE,CAAC;IACN7B,CAAC,IAAI6B,CAAC;EACR;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtBD,GAAG,CAACC,CAAC,CAAC,IAAI,CAAC,GAAGF,CAAC,GAAG3B,CAAC;IACnB4B,GAAG,CAACC,CAAC,CAAC,IAAIF,CAAC;EACb;EACA,OAAOC,GAAG;AACZ;AACA,SAASE,QAAQA,CAACpB,CAAC,EAAEC,CAAC,EAAEX,CAAC,EAAEE,CAAC,EAAEjB,GAAG,EAAE;EACjC,IAAIyB,CAAC,KAAKzB,GAAG,EAAE;IACb,OAAQ,CAAC0B,CAAC,GAAGX,CAAC,IAAIE,CAAC,IAAKS,CAAC,GAAGX,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC;EACA,IAAIW,CAAC,KAAK1B,GAAG,EAAE;IACb,OAAO,CAACe,CAAC,GAAGU,CAAC,IAAIR,CAAC,GAAG,CAAC;EACxB;EACA,OAAO,CAACQ,CAAC,GAAGC,CAAC,IAAIT,CAAC,GAAG,CAAC;AACxB;AACA,SAAS6B,OAAOA,CAACnD,CAAC,EAAE;EAClB,MAAMoD,KAAK,GAAG,GAAG;EACjB,MAAMtB,CAAC,GAAG9B,CAAC,CAAC8B,CAAC,GAAGsB,KAAK;EACrB,MAAMrB,CAAC,GAAG/B,CAAC,CAAC+B,CAAC,GAAGqB,KAAK;EACrB,MAAMhC,CAAC,GAAGpB,CAAC,CAACoB,CAAC,GAAGgC,KAAK;EACrB,MAAM/C,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACyB,CAAC,EAAEC,CAAC,EAAEX,CAAC,CAAC;EAC7B,MAAMd,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACwB,CAAC,EAAEC,CAAC,EAAEX,CAAC,CAAC;EAC7B,MAAMlB,CAAC,GAAG,CAACG,GAAG,GAAGC,GAAG,IAAI,CAAC;EACzB,IAAIH,CAAC,EAAEuC,CAAC,EAAEpB,CAAC;EACX,IAAIjB,GAAG,KAAKC,GAAG,EAAE;IACfgB,CAAC,GAAGjB,GAAG,GAAGC,GAAG;IACboC,CAAC,GAAGxC,CAAC,GAAG,GAAG,GAAGoB,CAAC,IAAI,CAAC,GAAGjB,GAAG,GAAGC,GAAG,CAAC,GAAGgB,CAAC,IAAIjB,GAAG,GAAGC,GAAG,CAAC;IACnDH,CAAC,GAAG+C,QAAQ,CAACpB,CAAC,EAAEC,CAAC,EAAEX,CAAC,EAAEE,CAAC,EAAEjB,GAAG,CAAC;IAC7BF,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,GAAG;EAClB;EACA,OAAO,CAACA,CAAC,GAAG,CAAC,EAAEuC,CAAC,IAAI,CAAC,EAAExC,CAAC,CAAC;AAC3B;AACA,SAASmD,KAAKA,CAAC7B,CAAC,EAAEL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO,CACLiC,KAAK,CAACC,OAAO,CAACpC,CAAC,CAAC,GACZK,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GACnBK,CAAC,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EACdmC,GAAG,CAAC/C,GAAG,CAAC;AACZ;AACA,SAASgD,OAAOA,CAACtD,CAAC,EAAEuC,CAAC,EAAExC,CAAC,EAAE;EACxB,OAAOmD,KAAK,CAACZ,QAAQ,EAAEtC,CAAC,EAAEuC,CAAC,EAAExC,CAAC,CAAC;AACjC;AACA,SAASwD,OAAOA,CAACvD,CAAC,EAAE4C,CAAC,EAAE3B,CAAC,EAAE;EACxB,OAAOiC,KAAK,CAACP,QAAQ,EAAE3C,CAAC,EAAE4C,CAAC,EAAE3B,CAAC,CAAC;AACjC;AACA,SAASuC,OAAOA,CAACxD,CAAC,EAAEuC,CAAC,EAAE1C,CAAC,EAAE;EACxB,OAAOqD,KAAK,CAACR,QAAQ,EAAE1C,CAAC,EAAEuC,CAAC,EAAE1C,CAAC,CAAC;AACjC;AACA,SAAS4D,GAAGA,CAACzD,CAAC,EAAE;EACd,OAAO,CAACA,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG;AAC9B;AACA,SAAS0D,QAAQA,CAAC5B,GAAG,EAAE;EACrB,MAAM6B,CAAC,GAAGtB,MAAM,CAACuB,IAAI,CAAC9B,GAAG,CAAC;EAC1B,IAAId,CAAC,GAAG,GAAG;EACX,IAAInB,CAAC;EACL,IAAI,CAAC8D,CAAC,EAAE;IACN;EACF;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK9D,CAAC,EAAE;IACdmB,CAAC,GAAG2C,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAAC,CAACuD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGrD,GAAG,CAAC,CAACqD,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC;EACA,MAAM3D,CAAC,GAAGyD,GAAG,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,MAAME,EAAE,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,MAAMG,EAAE,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IAClB9D,CAAC,GAAG0D,OAAO,CAACvD,CAAC,EAAE6D,EAAE,EAAEC,EAAE,CAAC;EACxB,CAAC,MAAM,IAAIH,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IACzB9D,CAAC,GAAG2D,OAAO,CAACxD,CAAC,EAAE6D,EAAE,EAAEC,EAAE,CAAC;EACxB,CAAC,MAAM;IACLjE,CAAC,GAAGyD,OAAO,CAACtD,CAAC,EAAE6D,EAAE,EAAEC,EAAE,CAAC;EACxB;EACA,OAAO;IACLnC,CAAC,EAAE9B,CAAC,CAAC,CAAC,CAAC;IACP+B,CAAC,EAAE/B,CAAC,CAAC,CAAC,CAAC;IACPoB,CAAC,EAAEpB,CAAC,CAAC,CAAC,CAAC;IACPmB,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAAS+C,MAAMA,CAAClE,CAAC,EAAEmE,GAAG,EAAE;EACtB,IAAIhE,CAAC,GAAGgD,OAAO,CAACnD,CAAC,CAAC;EAClBG,CAAC,CAAC,CAAC,CAAC,GAAGyD,GAAG,CAACzD,CAAC,CAAC,CAAC,CAAC,GAAGgE,GAAG,CAAC;EACtBhE,CAAC,GAAGsD,OAAO,CAACtD,CAAC,CAAC;EACdH,CAAC,CAAC8B,CAAC,GAAG3B,CAAC,CAAC,CAAC,CAAC;EACVH,CAAC,CAAC+B,CAAC,GAAG5B,CAAC,CAAC,CAAC,CAAC;EACVH,CAAC,CAACoB,CAAC,GAAGjB,CAAC,CAAC,CAAC,CAAC;AACZ;AACA,SAASiE,SAASA,CAACpE,CAAC,EAAE;EACpB,IAAI,CAACA,CAAC,EAAE;IACN;EACF;EACA,MAAMmB,CAAC,GAAGgC,OAAO,CAACnD,CAAC,CAAC;EACpB,MAAMG,CAAC,GAAGgB,CAAC,CAAC,CAAC,CAAC;EACd,MAAMuB,CAAC,GAAG/B,GAAG,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,MAAMjB,CAAC,GAAGS,GAAG,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOnB,CAAC,CAACmB,CAAC,GAAG,GAAG,GACZ,QAAQhB,CAAC,KAAKuC,CAAC,MAAMxC,CAAC,MAAMQ,GAAG,CAACV,CAAC,CAACmB,CAAC,CAAC,GAAG,GACvC,OAAOhB,CAAC,KAAKuC,CAAC,MAAMxC,CAAC,IAAI;AAC/B;AAEA,MAAMsD,GAAG,GAAG;EACXa,CAAC,EAAE,MAAM;EACTC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,KAAK;EACRC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,QAAQ;EACXC,CAAC,EAAE,OAAO;EACV9D,CAAC,EAAE,IAAI;EACP+D,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACP/D,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,OAAO;EACV8D,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,UAAU;EACb9D,CAAC,EAAE,IAAI;EACP+D,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVlE,CAAC,EAAE,IAAI;EACPmE,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,MAAM;EACTC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE;AACJ,CAAC;AACD,MAAMC,OAAO,GAAG;EACfC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,GAAG;EACVC,YAAY,EAAE,QAAQ;EACtBC,EAAE,EAAE,IAAI;EACRC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,MAAM;EACXC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,QAAQ;EACnBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE,QAAQ;EACbC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,QAAQ;EACrBC,EAAE,EAAE,QAAQ;EACZC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE;AACR,CAAC;AACD,SAASC,MAAMA,CAAA,EAAG;EAChB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACvJ,OAAO,CAAC;EACjC,MAAMyJ,KAAK,GAAGD,MAAM,CAACD,IAAI,CAACzL,GAAG,CAAC;EAC9B,IAAIP,CAAC,EAAEmM,CAAC,EAAExM,CAAC,EAAEyM,EAAE,EAAEC,EAAE;EACnB,KAAKrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgM,IAAI,CAAC9M,MAAM,EAAEc,CAAC,EAAE,EAAE;IAChCoM,EAAE,GAAGC,EAAE,GAAGL,IAAI,CAAChM,CAAC,CAAC;IACjB,KAAKmM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAChN,MAAM,EAAEiN,CAAC,EAAE,EAAE;MACjCxM,CAAC,GAAGuM,KAAK,CAACC,CAAC,CAAC;MACZE,EAAE,GAAGA,EAAE,CAACC,OAAO,CAAC3M,CAAC,EAAEY,GAAG,CAACZ,CAAC,CAAC,CAAC;IAC5B;IACAA,CAAC,GAAG4M,QAAQ,CAAC9J,OAAO,CAAC2J,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7BL,QAAQ,CAACM,EAAE,CAAC,GAAG,CAAC1M,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,GAAG,IAAI,CAAC;EAC1D;EACA,OAAOoM,QAAQ;AACjB;AAEA,IAAIS,KAAK;AACT,SAASC,SAASA,CAACzN,GAAG,EAAE;EACtB,IAAI,CAACwN,KAAK,EAAE;IACVA,KAAK,GAAGV,MAAM,CAAC,CAAC;IAChBU,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC;EACA,MAAMxO,CAAC,GAAGsO,KAAK,CAACxN,GAAG,CAAC2N,WAAW,CAAC,CAAC,CAAC;EAClC,OAAOzO,CAAC,IAAI;IACVW,CAAC,EAAEX,CAAC,CAAC,CAAC,CAAC;IACPY,CAAC,EAAEZ,CAAC,CAAC,CAAC,CAAC;IACPC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IACPA,CAAC,EAAEA,CAAC,CAACgB,MAAM,KAAK,CAAC,GAAGhB,CAAC,CAAC,CAAC,CAAC,GAAG;EAC7B,CAAC;AACH;AAEA,MAAM0O,MAAM,GAAG,sGAAsG;AACrH,SAASC,QAAQA,CAAC7N,GAAG,EAAE;EACrB,MAAM6B,CAAC,GAAG+L,MAAM,CAAC9L,IAAI,CAAC9B,GAAG,CAAC;EAC1B,IAAId,CAAC,GAAG,GAAG;EACX,IAAIW,CAAC,EAAEC,CAAC,EAAEX,CAAC;EACX,IAAI,CAAC0C,CAAC,EAAE;IACN;EACF;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAKhC,CAAC,EAAE;IACd,MAAM9B,CAAC,GAAG,CAAC8D,CAAC,CAAC,CAAC,CAAC;IACf3C,CAAC,GAAG2C,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAACP,CAAC,CAAC,GAAGC,GAAG,CAACD,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1C;EACA8B,CAAC,GAAG,CAACgC,CAAC,CAAC,CAAC,CAAC;EACT/B,CAAC,GAAG,CAAC+B,CAAC,CAAC,CAAC,CAAC;EACT1C,CAAC,GAAG,CAAC0C,CAAC,CAAC,CAAC,CAAC;EACThC,CAAC,GAAG,GAAG,IAAIgC,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAACuB,CAAC,CAAC,GAAG7B,GAAG,CAAC6B,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1CC,CAAC,GAAG,GAAG,IAAI+B,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAACwB,CAAC,CAAC,GAAG9B,GAAG,CAAC8B,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1CX,CAAC,GAAG,GAAG,IAAI0C,CAAC,CAAC,CAAC,CAAC,GAAGvD,GAAG,CAACa,CAAC,CAAC,GAAGnB,GAAG,CAACmB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1C,OAAO;IACLU,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJX,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAAS4O,SAASA,CAAC/P,CAAC,EAAE;EACpB,OAAOA,CAAC,KACNA,CAAC,CAACmB,CAAC,GAAG,GAAG,GACL,QAAQnB,CAAC,CAAC8B,CAAC,KAAK9B,CAAC,CAAC+B,CAAC,KAAK/B,CAAC,CAACoB,CAAC,KAAKV,GAAG,CAACV,CAAC,CAACmB,CAAC,CAAC,GAAG,GAC3C,OAAOnB,CAAC,CAAC8B,CAAC,KAAK9B,CAAC,CAAC+B,CAAC,KAAK/B,CAAC,CAACoB,CAAC,GAAG,CAClC;AACH;AAEA,MAAM4O,EAAE,GAAGhQ,CAAC,IAAIA,CAAC,IAAI,SAAS,GAAGA,CAAC,GAAG,KAAK,GAAGI,IAAI,CAAC6P,GAAG,CAACjQ,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;AACnF,MAAMkQ,IAAI,GAAGlQ,CAAC,IAAIA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGI,IAAI,CAAC6P,GAAG,CAAC,CAACjQ,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;AAC/E,SAASmQ,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAE;EAClC,MAAMxO,CAAC,GAAGoO,IAAI,CAACxP,GAAG,CAAC0P,IAAI,CAACtO,CAAC,CAAC,CAAC;EAC3B,MAAMC,CAAC,GAAGmO,IAAI,CAACxP,GAAG,CAAC0P,IAAI,CAACrO,CAAC,CAAC,CAAC;EAC3B,MAAMX,CAAC,GAAG8O,IAAI,CAACxP,GAAG,CAAC0P,IAAI,CAAChP,CAAC,CAAC,CAAC;EAC3B,OAAO;IACLU,CAAC,EAAErB,GAAG,CAACuP,EAAE,CAAClO,CAAC,GAAGwO,CAAC,IAAIJ,IAAI,CAACxP,GAAG,CAAC2P,IAAI,CAACvO,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3CC,CAAC,EAAEtB,GAAG,CAACuP,EAAE,CAACjO,CAAC,GAAGuO,CAAC,IAAIJ,IAAI,CAACxP,GAAG,CAAC2P,IAAI,CAACtO,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3CX,CAAC,EAAEX,GAAG,CAACuP,EAAE,CAAC5O,CAAC,GAAGkP,CAAC,IAAIJ,IAAI,CAACxP,GAAG,CAAC2P,IAAI,CAACjP,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3CD,CAAC,EAAEiP,IAAI,CAACjP,CAAC,GAAGmP,CAAC,IAAID,IAAI,CAAClP,CAAC,GAAGiP,IAAI,CAACjP,CAAC;EAClC,CAAC;AACH;AAEA,SAASoP,MAAMA,CAACvQ,CAAC,EAAEiD,CAAC,EAAEuN,KAAK,EAAE;EAC3B,IAAIxQ,CAAC,EAAE;IACL,IAAIyQ,GAAG,GAAGtN,OAAO,CAACnD,CAAC,CAAC;IACpByQ,GAAG,CAACxN,CAAC,CAAC,GAAG7C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACmQ,GAAG,CAACxN,CAAC,CAAC,GAAGwN,GAAG,CAACxN,CAAC,CAAC,GAAGuN,KAAK,EAAEvN,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1EwN,GAAG,GAAGhN,OAAO,CAACgN,GAAG,CAAC;IAClBzQ,CAAC,CAAC8B,CAAC,GAAG2O,GAAG,CAAC,CAAC,CAAC;IACZzQ,CAAC,CAAC+B,CAAC,GAAG0O,GAAG,CAAC,CAAC,CAAC;IACZzQ,CAAC,CAACoB,CAAC,GAAGqP,GAAG,CAAC,CAAC,CAAC;EACd;AACF;AACA,SAASC,KAAKA,CAAC1Q,CAAC,EAAE2Q,KAAK,EAAE;EACvB,OAAO3Q,CAAC,GAAGkP,MAAM,CAAC0B,MAAM,CAACD,KAAK,IAAI,CAAC,CAAC,EAAE3Q,CAAC,CAAC,GAAGA,CAAC;AAC9C;AACA,SAAS6Q,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI9Q,CAAC,GAAG;IAAC8B,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEX,CAAC,EAAE,CAAC;IAAED,CAAC,EAAE;EAAG,CAAC;EAClC,IAAImC,KAAK,CAACC,OAAO,CAACuN,KAAK,CAAC,EAAE;IACxB,IAAIA,KAAK,CAAC3O,MAAM,IAAI,CAAC,EAAE;MACrBnC,CAAC,GAAG;QAAC8B,CAAC,EAAEgP,KAAK,CAAC,CAAC,CAAC;QAAE/O,CAAC,EAAE+O,KAAK,CAAC,CAAC,CAAC;QAAE1P,CAAC,EAAE0P,KAAK,CAAC,CAAC,CAAC;QAAE3P,CAAC,EAAE;MAAG,CAAC;MACnD,IAAI2P,KAAK,CAAC3O,MAAM,GAAG,CAAC,EAAE;QACpBnC,CAAC,CAACmB,CAAC,GAAGV,GAAG,CAACqQ,KAAK,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC,MAAM;IACL9Q,CAAC,GAAG0Q,KAAK,CAACI,KAAK,EAAE;MAAChP,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEX,CAAC,EAAE,CAAC;MAAED,CAAC,EAAE;IAAC,CAAC,CAAC;IAC1CnB,CAAC,CAACmB,CAAC,GAAGV,GAAG,CAACT,CAAC,CAACmB,CAAC,CAAC;EAChB;EACA,OAAOnB,CAAC;AACV;AACA,SAAS+Q,aAAaA,CAAC9O,GAAG,EAAE;EAC1B,IAAIA,GAAG,CAAC+O,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,OAAOlB,QAAQ,CAAC7N,GAAG,CAAC;EACtB;EACA,OAAO4B,QAAQ,CAAC5B,GAAG,CAAC;AACtB;AACA,MAAMgP,KAAK,CAAC;EACVC,WAAWA,CAACJ,KAAK,EAAE;IACjB,IAAIA,KAAK,YAAYG,KAAK,EAAE;MAC1B,OAAOH,KAAK;IACd;IACA,MAAMK,IAAI,GAAG,OAAOL,KAAK;IACzB,IAAI9Q,CAAC;IACL,IAAImR,IAAI,KAAK,QAAQ,EAAE;MACrBnR,CAAC,GAAG6Q,UAAU,CAACC,KAAK,CAAC;IACvB,CAAC,MAAM,IAAIK,IAAI,KAAK,QAAQ,EAAE;MAC5BnR,CAAC,GAAGgC,QAAQ,CAAC8O,KAAK,CAAC,IAAIpB,SAAS,CAACoB,KAAK,CAAC,IAAIC,aAAa,CAACD,KAAK,CAAC;IACjE;IACA,IAAI,CAACM,IAAI,GAAGpR,CAAC;IACb,IAAI,CAACqR,MAAM,GAAG,CAAC,CAACrR,CAAC;EACnB;EACA,IAAIsR,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,MAAM;EACpB;EACA,IAAIrO,GAAGA,CAAA,EAAG;IACR,IAAIhD,CAAC,GAAG0Q,KAAK,CAAC,IAAI,CAACU,IAAI,CAAC;IACxB,IAAIpR,CAAC,EAAE;MACLA,CAAC,CAACmB,CAAC,GAAGT,GAAG,CAACV,CAAC,CAACmB,CAAC,CAAC;IAChB;IACA,OAAOnB,CAAC;EACV;EACA,IAAIgD,GAAGA,CAACuO,GAAG,EAAE;IACX,IAAI,CAACH,IAAI,GAAGP,UAAU,CAACU,GAAG,CAAC;EAC7B;EACAxB,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsB,MAAM,GAAGtB,SAAS,CAAC,IAAI,CAACqB,IAAI,CAAC,GAAG7O,SAAS;EACvD;EACAD,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC+O,MAAM,GAAG/O,SAAS,CAAC,IAAI,CAAC8O,IAAI,CAAC,GAAG7O,SAAS;EACvD;EACA6B,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACiN,MAAM,GAAGjN,SAAS,CAAC,IAAI,CAACgN,IAAI,CAAC,GAAG7O,SAAS;EACvD;EACAiP,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACjB,IAAID,KAAK,EAAE;MACT,MAAME,EAAE,GAAG,IAAI,CAAC3O,GAAG;MACnB,MAAM4O,EAAE,GAAGH,KAAK,CAACzO,GAAG;MACpB,IAAI6O,EAAE;MACN,MAAMC,CAAC,GAAGJ,MAAM,KAAKG,EAAE,GAAG,GAAG,GAAGH,MAAM;MACtC,MAAM3O,CAAC,GAAG,CAAC,GAAG+O,CAAC,GAAG,CAAC;MACnB,MAAM3Q,CAAC,GAAGwQ,EAAE,CAACxQ,CAAC,GAAGyQ,EAAE,CAACzQ,CAAC;MACrB,MAAM4Q,EAAE,GAAG,CAAC,CAAChP,CAAC,GAAG5B,CAAC,KAAK,CAAC,CAAC,GAAG4B,CAAC,GAAG,CAACA,CAAC,GAAG5B,CAAC,KAAK,CAAC,GAAG4B,CAAC,GAAG5B,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG;MACjE0Q,EAAE,GAAG,CAAC,GAAGE,EAAE;MACXJ,EAAE,CAAC7P,CAAC,GAAG,IAAI,GAAGiQ,EAAE,GAAGJ,EAAE,CAAC7P,CAAC,GAAG+P,EAAE,GAAGD,EAAE,CAAC9P,CAAC,GAAG,GAAG;MACzC6P,EAAE,CAAC5P,CAAC,GAAG,IAAI,GAAGgQ,EAAE,GAAGJ,EAAE,CAAC5P,CAAC,GAAG8P,EAAE,GAAGD,EAAE,CAAC7P,CAAC,GAAG,GAAG;MACzC4P,EAAE,CAACvQ,CAAC,GAAG,IAAI,GAAG2Q,EAAE,GAAGJ,EAAE,CAACvQ,CAAC,GAAGyQ,EAAE,GAAGD,EAAE,CAACxQ,CAAC,GAAG,GAAG;MACzCuQ,EAAE,CAACxQ,CAAC,GAAG2Q,CAAC,GAAGH,EAAE,CAACxQ,CAAC,GAAG,CAAC,CAAC,GAAG2Q,CAAC,IAAIF,EAAE,CAACzQ,CAAC;MAChC,IAAI,CAAC6B,GAAG,GAAG2O,EAAE;IACf;IACA,OAAO,IAAI;EACb;EACAxB,WAAWA,CAACsB,KAAK,EAAEnB,CAAC,EAAE;IACpB,IAAImB,KAAK,EAAE;MACT,IAAI,CAACL,IAAI,GAAGjB,WAAW,CAAC,IAAI,CAACiB,IAAI,EAAEK,KAAK,CAACL,IAAI,EAAEd,CAAC,CAAC;IACnD;IACA,OAAO,IAAI;EACb;EACAI,KAAKA,CAAA,EAAG;IACN,OAAO,IAAIO,KAAK,CAAC,IAAI,CAACjO,GAAG,CAAC;EAC5B;EACAX,KAAKA,CAAClB,CAAC,EAAE;IACP,IAAI,CAACiQ,IAAI,CAACjQ,CAAC,GAAGV,GAAG,CAACU,CAAC,CAAC;IACpB,OAAO,IAAI;EACb;EACA6Q,OAAOA,CAACxB,KAAK,EAAE;IACb,MAAMxN,GAAG,GAAG,IAAI,CAACoO,IAAI;IACrBpO,GAAG,CAAC7B,CAAC,IAAI,CAAC,GAAGqP,KAAK;IAClB,OAAO,IAAI;EACb;EACAyB,SAASA,CAAA,EAAG;IACV,MAAMjP,GAAG,GAAG,IAAI,CAACoO,IAAI;IACrB,MAAMc,GAAG,GAAGnS,KAAK,CAACiD,GAAG,CAAClB,CAAC,GAAG,GAAG,GAAGkB,GAAG,CAACjB,CAAC,GAAG,IAAI,GAAGiB,GAAG,CAAC5B,CAAC,GAAG,IAAI,CAAC;IAC5D4B,GAAG,CAAClB,CAAC,GAAGkB,GAAG,CAACjB,CAAC,GAAGiB,GAAG,CAAC5B,CAAC,GAAG8Q,GAAG;IAC3B,OAAO,IAAI;EACb;EACAC,OAAOA,CAAC3B,KAAK,EAAE;IACb,MAAMxN,GAAG,GAAG,IAAI,CAACoO,IAAI;IACrBpO,GAAG,CAAC7B,CAAC,IAAI,CAAC,GAAGqP,KAAK;IAClB,OAAO,IAAI;EACb;EACA4B,MAAMA,CAAA,EAAG;IACP,MAAMpS,CAAC,GAAG,IAAI,CAACoR,IAAI;IACnBpR,CAAC,CAAC8B,CAAC,GAAG,GAAG,GAAG9B,CAAC,CAAC8B,CAAC;IACf9B,CAAC,CAAC+B,CAAC,GAAG,GAAG,GAAG/B,CAAC,CAAC+B,CAAC;IACf/B,CAAC,CAACoB,CAAC,GAAG,GAAG,GAAGpB,CAAC,CAACoB,CAAC;IACf,OAAO,IAAI;EACb;EACAiR,OAAOA,CAAC7B,KAAK,EAAE;IACbD,MAAM,CAAC,IAAI,CAACa,IAAI,EAAE,CAAC,EAAEZ,KAAK,CAAC;IAC3B,OAAO,IAAI;EACb;EACA8B,MAAMA,CAAC9B,KAAK,EAAE;IACZD,MAAM,CAAC,IAAI,CAACa,IAAI,EAAE,CAAC,EAAE,CAACZ,KAAK,CAAC;IAC5B,OAAO,IAAI;EACb;EACA+B,QAAQA,CAAC/B,KAAK,EAAE;IACdD,MAAM,CAAC,IAAI,CAACa,IAAI,EAAE,CAAC,EAAEZ,KAAK,CAAC;IAC3B,OAAO,IAAI;EACb;EACAgC,UAAUA,CAAChC,KAAK,EAAE;IAChBD,MAAM,CAAC,IAAI,CAACa,IAAI,EAAE,CAAC,EAAE,CAACZ,KAAK,CAAC;IAC5B,OAAO,IAAI;EACb;EACAtM,MAAMA,CAACC,GAAG,EAAE;IACVD,MAAM,CAAC,IAAI,CAACkN,IAAI,EAAEjN,GAAG,CAAC;IACtB,OAAO,IAAI;EACb;AACF;AAEA,SAASsO,SAASA,CAAC3B,KAAK,EAAE;EACxB,OAAO,IAAIG,KAAK,CAACH,KAAK,CAAC;AACzB;AAEA,SAASG,KAAK,EAAEvQ,GAAG,EAAEF,GAAG,EAAEiS,SAAS,IAAIC,OAAO,EAAE1Q,QAAQ,EAAEM,SAAS,EAAEmB,OAAO,EAAEW,SAAS,EAAET,OAAO,EAAEE,QAAQ,EAAEH,OAAO,EAAEzD,GAAG,EAAEQ,GAAG,EAAEE,GAAG,EAAE+O,SAAS,EAAEnP,GAAG,EAAE4C,OAAO,EAAE2M,QAAQ,EAAEC,SAAS,EAAE7L,MAAM,EAAEnE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}