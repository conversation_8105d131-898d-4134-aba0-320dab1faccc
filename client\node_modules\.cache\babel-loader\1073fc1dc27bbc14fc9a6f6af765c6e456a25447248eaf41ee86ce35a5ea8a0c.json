{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ZapOff = createLucideIcon(\"ZapOff\", [[\"polyline\", {\n  points: \"12.41 6.75 13 2 10.57 4.92\",\n  key: \"122m05\"\n}], [\"polyline\", {\n  points: \"18.57 12.91 21 10 15.66 10\",\n  key: \"16r43o\"\n}], [\"polyline\", {\n  points: \"8 8 3 14 12 14 11 22 16 16\",\n  key: \"tmh4bc\"\n}], [\"line\", {\n  x1: \"2\",\n  y1: \"2\",\n  x2: \"22\",\n  y2: \"22\",\n  key: \"1w4vcy\"\n}]]);\nexport { ZapOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "points", "key", "x1", "y1", "x2", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\zap-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst ZapOff = createLucideIcon('ZapOff', [\n  ['polyline', { points: '12.41 6.75 13 2 10.57 4.92', key: '122m05' }],\n  ['polyline', { points: '18.57 12.91 21 10 15.66 10', key: '16r43o' }],\n  ['polyline', { points: '8 8 3 14 12 14 11 22 16 16', key: 'tmh4bc' }],\n  ['line', { x1: '2', y1: '2', x2: '22', y2: '22', key: '1w4vcy' }],\n]);\n\nexport default ZapOff;\n"], "mappings": ";;;;;AAEM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,UAAY;EAAEC,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAED,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAED,MAAA,EAAQ,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}