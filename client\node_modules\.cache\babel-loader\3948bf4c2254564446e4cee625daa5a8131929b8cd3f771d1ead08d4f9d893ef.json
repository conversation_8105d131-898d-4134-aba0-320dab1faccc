{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Zap = createLucideIcon(\"Zap\", [[\"polygon\", {\n  points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n  key: \"45s27k\"\n}]]);\nexport { Zap as default };", "map": {"version": 3, "names": ["Zap", "createLucideIcon", "points", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\zap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst Zap = createLucideIcon('Zap', [\n  [\n    'polygon',\n    { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' },\n  ],\n]);\n\nexport default Zap;\n"], "mappings": ";;;;;AAEM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,WACA;EAAEC,MAAA,EAAQ,wCAA0C;EAAAC,GAAA,EAAK;AAAS,EACpE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}