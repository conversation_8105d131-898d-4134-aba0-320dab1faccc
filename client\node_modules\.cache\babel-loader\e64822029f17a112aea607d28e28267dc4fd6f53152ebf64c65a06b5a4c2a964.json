{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Send = createLucideIcon(\"Send\", [[\"line\", {\n  x1: \"22\",\n  y1: \"2\",\n  x2: \"11\",\n  y2: \"13\",\n  key: \"10auo0\"\n}], [\"polygon\", {\n  points: \"22 2 15 22 11 13 2 9 22 2\",\n  key: \"12uapv\"\n}]]);\nexport { Send as default };", "map": {"version": 3, "names": ["Send", "createLucideIcon", "x1", "y1", "x2", "y2", "key", "points"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst Send = createLucideIcon('Send', [\n  ['line', { x1: '22', y1: '2', x2: '11', y2: '13', key: '10auo0' }],\n  ['polygon', { points: '22 2 15 22 11 13 2 9 22 2', key: '12uapv' }],\n]);\n\nexport default Send;\n"], "mappings": ";;;;;AAEM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,SAAW;EAAEC,MAAA,EAAQ,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}