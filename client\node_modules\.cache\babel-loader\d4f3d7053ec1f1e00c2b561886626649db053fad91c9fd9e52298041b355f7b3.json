{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst View = createLucideIcon(\"View\", [[\"path\", {\n  d: \"M5 12s2.545-5 7-5c4.454 0 7 5 7 5s-2.546 5-7 5c-4.455 0-7-5-7-5z\",\n  key: \"vptub8\"\n}], [\"path\", {\n  d: \"M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2z\",\n  key: \"10lhjs\"\n}], [\"path\", {\n  d: \"M21 17v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2\",\n  key: \"mrq65r\"\n}], [\"path\", {\n  d: \"M21 7V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2\",\n  key: \"be3xqs\"\n}]]);\nexport { View as default };", "map": {"version": 3, "names": ["View", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\view.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst View = createLucideIcon('View', [\n  [\n    'path',\n    {\n      d: 'M5 12s2.545-5 7-5c4.454 0 7 5 7 5s-2.546 5-7 5c-4.455 0-7-5-7-5z',\n      key: 'vptub8',\n    },\n  ],\n  ['path', { d: 'M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2z', key: '10lhjs' }],\n  ['path', { d: 'M21 17v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2', key: 'mrq65r' }],\n  ['path', { d: 'M21 7V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2', key: 'be3xqs' }],\n]);\n\nexport default View;\n"], "mappings": ";;;;;AAEM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}