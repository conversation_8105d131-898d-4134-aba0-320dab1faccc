{"ast": null, "code": "\"use client\";\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\freelance-marketplace setup\\\\freelance-marketplace setup\\\\client\\\\src\\\\pages\\\\auth\\\\LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../../contexts/AuthContext\";\nimport Alert from \"../../components/common/Alert\";\nimport Button from \"../../components/common/Button\";\nimport Spinner from \"../../components/common/Spinner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError(\"\");\n    try {\n      setLoading(true);\n      console.log(\"Attempting login with:\", formData);\n      console.log(\"API base URL:\", process.env.REACT_APP_API_URL || \"http://localhost:3000/api\");\n\n      // Validate input\n      if (!formData.email || !formData.password) {\n        throw new Error(\"Please enter both email and password\");\n      }\n\n      // Try a direct fetch to test the connection\n      try {\n        const testResponse = await fetch(\"http://localhost:3002/api/auth/login\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(formData)\n        });\n        console.log(\"Direct fetch test response status:\", testResponse.status);\n        const testData = await testResponse.json();\n        console.log(\"Direct fetch test response data:\", testData);\n      } catch (testErr) {\n        console.error(\"Direct fetch test failed:\", testErr);\n      }\n\n      // Proceed with the regular login\n      const user = await login(formData);\n      console.log(\"Login successful:\", user);\n\n      // Redirect based on user role\n      if (user.role === \"client\") {\n        navigate(\"/client/dashboard\");\n      } else if (user.role === \"freelancer\") {\n        navigate(\"/freelancer/dashboard\");\n      } else if (user.role === \"admin\") {\n        navigate(\"/admin/dashboard\");\n      } else {\n        navigate(\"/\");\n      }\n    } catch (err) {\n      console.error(\"Login error:\", err);\n      console.error(\"Login error details:\", {\n        code: err.code,\n        message: err.message,\n        response: err.response,\n        stack: err.stack\n      });\n\n      // Provide more detailed error messages\n      if (err.code === \"ECONNABORTED\") {\n        setError(\"Connection timeout. Please try again.\");\n      } else if (err.code === \"ERR_NETWORK\") {\n        setError(\"Network error. Please check your internet connection or the server might be down.\");\n      } else if (err.response) {\n        var _err$response$data;\n        // Server responded with an error\n        setError(((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || `Server error: ${err.response.status}`);\n      } else {\n        // Something else went wrong\n        setError(err.message || \"An unexpected error occurred. Please try again.\");\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-center text-3xl font-extrabold text-gray-900\",\n          children: \"SkillSwap\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-center text-sm text-gray-600\",\n        children: \"Sign in to your account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"error\",\n          dismissible: true,\n          onDismiss: () => setError(\"\"),\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-6\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: formData.email,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"current-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"remember-me\",\n                name: \"remember-me\",\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember-me\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/forgot-password\",\n                className: \"font-medium text-blue-600 hover:text-blue-500\",\n                children: \"Forgot your password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"primary\",\n              fullWidth: true,\n              disabled: loading,\n              children: [loading ? /*#__PURE__*/_jsxDEV(Spinner, {\n                size: \"sm\",\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 28\n              }, this) : null, \"Sign in\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full border-t border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex justify-center text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 bg-white text-gray-500\",\n                children: \"Or\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"http://localhost:3002/api/auth/google\",\n              className: \"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                viewBox: \"0 0 24 24\",\n                width: \"24\",\n                height: \"24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"g\", {\n                  transform: \"matrix(1, 0, 0, 1, 27.009001, -39.238998)\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    fill: \"#4285F4\",\n                    d: \"M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    fill: \"#34A853\",\n                    d: \"M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    fill: \"#FBBC05\",\n                    d: \"M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    fill: \"#EA4335\",\n                    d: \"M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), \"Sign in with Google\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"font-medium text-blue-600 hover:text-blue-500\",\n              children: \"Create a new account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"fUzqTp5bYbDD1WupPz6PhG6L/Ro=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "useState", "Link", "useNavigate", "useAuth", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "jsxDEV", "_jsxDEV", "LoginPage", "formData", "setFormData", "email", "password", "loading", "setLoading", "error", "setError", "login", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "console", "log", "process", "env", "REACT_APP_API_URL", "Error", "testResponse", "fetch", "method", "headers", "body", "JSON", "stringify", "status", "testData", "json", "testErr", "user", "role", "err", "code", "message", "response", "stack", "_err$response$data", "data", "className", "children", "to", "fileName", "lineNumber", "columnNumber", "variant", "dismissible", "on<PERSON><PERSON><PERSON>", "onSubmit", "htmlFor", "id", "type", "autoComplete", "required", "onChange", "fullWidth", "disabled", "size", "href", "viewBox", "width", "height", "xmlns", "transform", "fill", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/freelance-marketplace setup/freelance-marketplace setup/client/src/pages/auth/LoginPage.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { <PERSON>, useNavigate } from \"react-router-dom\"\r\nimport { useAuth } from \"../../contexts/AuthContext\"\r\nimport Alert from \"../../components/common/Alert\"\r\nimport Button from \"../../components/common/Button\"\r\nimport Spinner from \"../../components/common/Spinner\"\r\n\r\nconst LoginPage = () => {\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    password: \"\",\r\n  })\r\n  const [loading, setLoading] = useState(false)\r\n  const [error, setError] = useState(\"\")\r\n  const { login } = useAuth()\r\n  const navigate = useNavigate()\r\n\r\n  const handleChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value,\r\n    })\r\n  }\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault()\r\n    setError(\"\")\r\n\r\n    try {\r\n      setLoading(true)\r\n      console.log(\"Attempting login with:\", formData)\r\n      console.log(\"API base URL:\", process.env.REACT_APP_API_URL || \"http://localhost:3000/api\")\r\n\r\n      // Validate input\r\n      if (!formData.email || !formData.password) {\r\n        throw new Error(\"Please enter both email and password\")\r\n      }\r\n\r\n      // Try a direct fetch to test the connection\r\n      try {\r\n        const testResponse = await fetch(\"http://localhost:3002/api/auth/login\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(formData),\r\n        });\r\n\r\n        console.log(\"Direct fetch test response status:\", testResponse.status);\r\n        const testData = await testResponse.json();\r\n        console.log(\"Direct fetch test response data:\", testData);\r\n      } catch (testErr) {\r\n        console.error(\"Direct fetch test failed:\", testErr);\r\n      }\r\n\r\n      // Proceed with the regular login\r\n      const user = await login(formData)\r\n      console.log(\"Login successful:\", user)\r\n\r\n      // Redirect based on user role\r\n      if (user.role === \"client\") {\r\n        navigate(\"/client/dashboard\")\r\n      } else if (user.role === \"freelancer\") {\r\n        navigate(\"/freelancer/dashboard\")\r\n      } else if (user.role === \"admin\") {\r\n        navigate(\"/admin/dashboard\")\r\n      } else {\r\n        navigate(\"/\")\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Login error:\", err)\r\n      console.error(\"Login error details:\", {\r\n        code: err.code,\r\n        message: err.message,\r\n        response: err.response,\r\n        stack: err.stack\r\n      })\r\n\r\n      // Provide more detailed error messages\r\n      if (err.code === \"ECONNABORTED\") {\r\n        setError(\"Connection timeout. Please try again.\")\r\n      } else if (err.code === \"ERR_NETWORK\") {\r\n        setError(\"Network error. Please check your internet connection or the server might be down.\")\r\n      } else if (err.response) {\r\n        // Server responded with an error\r\n        setError(err.response.data?.error || `Server error: ${err.response.status}`)\r\n      } else {\r\n        // Something else went wrong\r\n        setError(err.message || \"An unexpected error occurred. Please try again.\")\r\n      }\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <Link to=\"/\">\r\n          <h2 className=\"text-center text-3xl font-extrabold text-gray-900\">SkillSwap</h2>\r\n        </Link>\r\n        <p className=\"mt-2 text-center text-sm text-gray-600\">Sign in to your account</p>\r\n      </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\r\n          {error && (\r\n            <Alert variant=\"error\" dismissible onDismiss={() => setError(\"\")}>\r\n              {error}\r\n            </Alert>\r\n          )}\r\n\r\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n            <div>\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={formData.password}\r\n                  onChange={handleChange}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center\">\r\n                <input\r\n                  id=\"remember-me\"\r\n                  name=\"remember-me\"\r\n                  type=\"checkbox\"\r\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                />\r\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\r\n                  Remember me\r\n                </label>\r\n              </div>\r\n\r\n              <div className=\"text-sm\">\r\n                <Link to=\"/forgot-password\" className=\"font-medium text-blue-600 hover:text-blue-500\">\r\n                  Forgot your password?\r\n                </Link>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <Button type=\"submit\" variant=\"primary\" fullWidth disabled={loading}>\r\n                {loading ? <Spinner size=\"sm\" className=\"mr-2\" /> : null}\r\n                Sign in\r\n              </Button>\r\n            </div>\r\n          </form>\r\n\r\n          <div className=\"mt-6\">\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <div className=\"w-full border-t border-gray-300\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-sm\">\r\n                <span className=\"px-2 bg-white text-gray-500\">Or</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-6\">\r\n              <a\r\n                href=\"http://localhost:3002/api/auth/google\"\r\n                className=\"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              >\r\n                <svg className=\"h-5 w-5 mr-2\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g transform=\"matrix(1, 0, 0, 1, 27.009001, -39.238998)\">\r\n                    <path fill=\"#4285F4\" d=\"M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z\" />\r\n                    <path fill=\"#34A853\" d=\"M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z\" />\r\n                    <path fill=\"#FBBC05\" d=\"M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z\" />\r\n                    <path fill=\"#EA4335\" d=\"M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z\" />\r\n                  </g>\r\n                </svg>\r\n                Sign in with Google\r\n              </a>\r\n            </div>\r\n\r\n            <div className=\"mt-6 text-center\">\r\n              <Link to=\"/register\" className=\"font-medium text-blue-600 hover:text-blue-500\">\r\n                Create a new account\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default LoginPage\r\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AAEZ,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAOC,MAAM,MAAM,gCAAgC;AACnD,OAAOC,OAAO,MAAM,iCAAiC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAX,EAAA;EACtB,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEkB;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1BV,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACW,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBT,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBY,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAElB,QAAQ,CAAC;MAC/CiB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B,CAAC;;MAE1F;MACA,IAAI,CAACrB,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;QACzC,MAAM,IAAImB,KAAK,CAAC,sCAAsC,CAAC;MACzD;;MAEA;MACA,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;UACvEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC7B,QAAQ;QAC/B,CAAC,CAAC;QAEFiB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEK,YAAY,CAACO,MAAM,CAAC;QACtE,MAAMC,QAAQ,GAAG,MAAMR,YAAY,CAACS,IAAI,CAAC,CAAC;QAC1Cf,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEa,QAAQ,CAAC;MAC3D,CAAC,CAAC,OAAOE,OAAO,EAAE;QAChBhB,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAE2B,OAAO,CAAC;MACrD;;MAEA;MACA,MAAMC,IAAI,GAAG,MAAM1B,KAAK,CAACR,QAAQ,CAAC;MAClCiB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgB,IAAI,CAAC;;MAEtC;MACA,IAAIA,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;QAC1B1B,QAAQ,CAAC,mBAAmB,CAAC;MAC/B,CAAC,MAAM,IAAIyB,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;QACrC1B,QAAQ,CAAC,uBAAuB,CAAC;MACnC,CAAC,MAAM,IAAIyB,IAAI,CAACC,IAAI,KAAK,OAAO,EAAE;QAChC1B,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,MAAM;QACLA,QAAQ,CAAC,GAAG,CAAC;MACf;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZnB,OAAO,CAACX,KAAK,CAAC,cAAc,EAAE8B,GAAG,CAAC;MAClCnB,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAE;QACpC+B,IAAI,EAAED,GAAG,CAACC,IAAI;QACdC,OAAO,EAAEF,GAAG,CAACE,OAAO;QACpBC,QAAQ,EAAEH,GAAG,CAACG,QAAQ;QACtBC,KAAK,EAAEJ,GAAG,CAACI;MACb,CAAC,CAAC;;MAEF;MACA,IAAIJ,GAAG,CAACC,IAAI,KAAK,cAAc,EAAE;QAC/B9B,QAAQ,CAAC,uCAAuC,CAAC;MACnD,CAAC,MAAM,IAAI6B,GAAG,CAACC,IAAI,KAAK,aAAa,EAAE;QACrC9B,QAAQ,CAAC,mFAAmF,CAAC;MAC/F,CAAC,MAAM,IAAI6B,GAAG,CAACG,QAAQ,EAAE;QAAA,IAAAE,kBAAA;QACvB;QACAlC,QAAQ,CAAC,EAAAkC,kBAAA,GAAAL,GAAG,CAACG,QAAQ,CAACG,IAAI,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBnC,KAAK,KAAI,iBAAiB8B,GAAG,CAACG,QAAQ,CAACT,MAAM,EAAE,CAAC;MAC9E,CAAC,MAAM;QACL;QACAvB,QAAQ,CAAC6B,GAAG,CAACE,OAAO,IAAI,iDAAiD,CAAC;MAC5E;IACF,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAK6C,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzF9C,OAAA;MAAK6C,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/C9C,OAAA,CAACP,IAAI;QAACsD,EAAE,EAAC,GAAG;QAAAD,QAAA,eACV9C,OAAA;UAAI6C,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAA3D,YAAA;UAAA4D,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAF,QAAA,EAAA3D,YAAA;QAAA4D,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACPlD,OAAA;QAAG6C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAA3D,YAAA;QAAA4D,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAF,QAAA,EAAA3D,YAAA;MAAA4D,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC,eAENlD,OAAA;MAAK6C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD9C,OAAA;QAAK6C,SAAS,EAAC,kDAAkD;QAAAC,QAAA,GAC9DtC,KAAK,iBACJR,OAAA,CAACJ,KAAK;UAACuD,OAAO,EAAC,OAAO;UAACC,WAAW;UAACC,SAAS,EAAEA,CAAA,KAAM5C,QAAQ,CAAC,EAAE,CAAE;UAAAqC,QAAA,EAC9DtC;QAAK;UAAAwC,QAAA,EAAA3D,YAAA;UAAA4D,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDlD,OAAA;UAAM6C,SAAS,EAAC,WAAW;UAACS,QAAQ,EAAErC,YAAa;UAAA6B,QAAA,gBACjD9C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOuD,OAAO,EAAC,OAAO;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9C,OAAA;gBACEwD,EAAE,EAAC,OAAO;gBACVzC,IAAI,EAAC,OAAO;gBACZ0C,IAAI,EAAC,OAAO;gBACZC,YAAY,EAAC,OAAO;gBACpBC,QAAQ;gBACR3C,KAAK,EAAEd,QAAQ,CAACE,KAAM;gBACtBwD,QAAQ,EAAEhD,YAAa;gBACvBiC,SAAS,EAAC;cAAiL;gBAAAG,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAC5L;YAAC;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOuD,OAAO,EAAC,UAAU;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAE,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9C,OAAA;gBACEwD,EAAE,EAAC,UAAU;gBACbzC,IAAI,EAAC,UAAU;gBACf0C,IAAI,EAAC,UAAU;gBACfC,YAAY,EAAC,kBAAkB;gBAC/BC,QAAQ;gBACR3C,KAAK,EAAEd,QAAQ,CAACG,QAAS;gBACzBuD,QAAQ,EAAEhD,YAAa;gBACvBiC,SAAS,EAAC;cAAiL;gBAAAG,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAC5L;YAAC;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9C,OAAA;cAAK6C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9C,OAAA;gBACEwD,EAAE,EAAC,aAAa;gBAChBzC,IAAI,EAAC,aAAa;gBAClB0C,IAAI,EAAC,UAAU;gBACfZ,SAAS,EAAC;cAAmE;gBAAAG,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFlD,OAAA;gBAAOuD,OAAO,EAAC,aAAa;gBAACV,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAE,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENlD,OAAA;cAAK6C,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtB9C,OAAA,CAACP,IAAI;gBAACsD,EAAE,EAAC,kBAAkB;gBAACF,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAE,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAA8C,QAAA,eACE9C,OAAA,CAACH,MAAM;cAAC4D,IAAI,EAAC,QAAQ;cAACN,OAAO,EAAC,SAAS;cAACU,SAAS;cAACC,QAAQ,EAAExD,OAAQ;cAAAwC,QAAA,GACjExC,OAAO,gBAAGN,OAAA,CAACF,OAAO;gBAACiE,IAAI,EAAC,IAAI;gBAAClB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAAG,IAAI,EAAC,SAE3D;YAAA;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAF,QAAA,EAAA3D,YAAA;UAAA4D,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPlD,OAAA;UAAK6C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9C,OAAA;YAAK6C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB9C,OAAA;cAAK6C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjD9C,OAAA;gBAAK6C,SAAS,EAAC;cAAiC;gBAAAG,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,sCAAsC;cAAAC,QAAA,eACnD9C,OAAA;gBAAM6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB9C,OAAA;cACEgE,IAAI,EAAC,uCAAuC;cAC5CnB,SAAS,EAAC,mOAAmO;cAAAC,QAAA,gBAE7O9C,OAAA;gBAAK6C,SAAS,EAAC,cAAc;gBAACoB,OAAO,EAAC,WAAW;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,KAAK,EAAC,4BAA4B;gBAAAtB,QAAA,eACzG9C,OAAA;kBAAGqE,SAAS,EAAC,2CAA2C;kBAAAvB,QAAA,gBACtD9C,OAAA;oBAAMsE,IAAI,EAAC,SAAS;oBAACC,CAAC,EAAC;kBAA2O;oBAAAvB,QAAA,EAAA3D,YAAA;oBAAA4D,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrQlD,OAAA;oBAAMsE,IAAI,EAAC,SAAS;oBAACC,CAAC,EAAC;kBAAiQ;oBAAAvB,QAAA,EAAA3D,YAAA;oBAAA4D,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3RlD,OAAA;oBAAMsE,IAAI,EAAC,SAAS;oBAACC,CAAC,EAAC;kBAAmQ;oBAAAvB,QAAA,EAAA3D,YAAA;oBAAA4D,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7RlD,OAAA;oBAAMsE,IAAI,EAAC,SAAS;oBAACC,CAAC,EAAC;kBAAgP;oBAAAvB,QAAA,EAAA3D,YAAA;oBAAA4D,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAF,QAAA,EAAA3D,YAAA;kBAAA4D,UAAA;kBAAAC,YAAA;gBAAA,OACzQ;cAAC;gBAAAF,QAAA,EAAA3D,YAAA;gBAAA4D,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,uBAER;YAAA;cAAAF,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B9C,OAAA,CAACP,IAAI;cAACsD,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE/E;cAAAE,QAAA,EAAA3D,YAAA;cAAA4D,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAF,QAAA,EAAA3D,YAAA;YAAA4D,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAF,QAAA,EAAA3D,YAAA;UAAA4D,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAF,QAAA,EAAA3D,YAAA;QAAA4D,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAF,QAAA,EAAA3D,YAAA;MAAA4D,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAF,QAAA,EAAA3D,YAAA;IAAA4D,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAA5D,EAAA,CA/MKW,SAAS;EAAA,QAOKN,OAAO,EACRD,WAAW;AAAA;AAAA8E,EAAA,GARxBvE,SAAS;AAiNf,eAAeA,SAAS;AAAA,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}