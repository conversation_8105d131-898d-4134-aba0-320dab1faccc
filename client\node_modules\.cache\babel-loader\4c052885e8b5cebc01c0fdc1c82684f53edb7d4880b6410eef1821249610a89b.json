{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ToggleRight = createLucideIcon(\"ToggleRight\", [[\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"20\",\n  height: \"12\",\n  rx: \"6\",\n  ry: \"6\",\n  key: \"cdopvd\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"4ma0v8\"\n}]]);\nexport { ToggleRight as default };", "map": {"version": 3, "names": ["ToggleRight", "createLucideIcon", "x", "y", "width", "height", "rx", "ry", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\toggle-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst ToggleRight = createLucideIcon('ToggleRight', [\n  [\n    'rect',\n    {\n      x: '2',\n      y: '6',\n      width: '20',\n      height: '12',\n      rx: '6',\n      ry: '6',\n      key: 'cdopvd',\n    },\n  ],\n  ['circle', { cx: '16', cy: '12', r: '2', key: '4ma0v8' }],\n]);\n\nexport default ToggleRight;\n"], "mappings": ";;;;;AAEM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,CAAG;EACHC,KAAO;EACPC,MAAQ;EACRC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}