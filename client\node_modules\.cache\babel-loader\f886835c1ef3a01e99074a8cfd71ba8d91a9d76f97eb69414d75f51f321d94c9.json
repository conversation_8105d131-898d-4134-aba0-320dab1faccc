{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Wallet = createLucideIcon(\"Wallet\", [[\"path\", {\n  d: \"M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4\",\n  key: \"st805m\"\n}], [\"path\", {\n  d: \"M4 6v12c0 1.1.9 2 2 2h14v-4\",\n  key: \"16cu1e\"\n}], [\"path\", {\n  d: \"M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z\",\n  key: \"lwd56p\"\n}]]);\nexport { Wallet as default };", "map": {"version": 3, "names": ["Wallet", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\wallet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst Wallet = createLucideIcon('Wallet', [\n  ['path', { d: 'M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4', key: 'st805m' }],\n  ['path', { d: 'M4 6v12c0 1.1.9 2 2 2h14v-4', key: '16cu1e' }],\n  ['path', { d: 'M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z', key: 'lwd56p' }],\n]);\n\nexport default Wallet;\n"], "mappings": ";;;;;AAEM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}