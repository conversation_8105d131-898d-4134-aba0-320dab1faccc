{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    children,\n    ...rest\n  }, ref) => createElement(\"svg\", {\n    ref,\n    ...defaultAttributes,\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth,\n    className: `lucide lucide-${toKebabCase(iconName)}`,\n    ...rest\n  }, [...iconNode.map(([tag, attrs]) => createElement(tag, attrs)), ...((Array.isArray(children) ? children : [children]) || [])]));\n  Component.displayName = `${iconName}`;\n  return Component;\n};\nexport { createLucideIcon as default, toKebabCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "color", "size", "strokeWidth", "children", "rest", "ref", "createElement", "defaultAttributes", "width", "height", "stroke", "className", "map", "tag", "attrs", "Array", "isArray", "displayName"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import { forwardRef, createElement, ReactSVG, SVGProps } from 'react';\nimport defaultAttributes from './defaultAttributes';\n\ntype IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][]\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>\n\nexport interface LucideProps extends SVGAttributes {\n  size?: string | number\n}\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) => string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth,\n          className: `lucide lucide-${toKebabCase(iconName)}`,\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(\n            (Array.isArray(children) ? children : [children]) || []\n          )\n        ],\n      ),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon\n"], "mappings": ";;;;;;AAkBa,MAAAA,WAAA,GAAeC,MAAmB,IAAAA,MAAA,CAAOC,OAAA,CAAQ,oBAAsB,SAAO,EAAEC,WAAY;AAEnG,MAAAC,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAuB;EACjE,MAAMC,SAAY,GAAAC,UAAA,CAChB,CAAC;IAAEC,KAAA,GAAQ,cAAgB;IAAAC,IAAA,GAAO,EAAI;IAAAC,WAAA,GAAc,CAAG;IAAAC,QAAA;IAAA,GAAaC;EAAK,GAAGC,GAC1E,KAAAC,aAAA,CACE,OACA;IACED,GAAA;IACA,GAAGE,iBAAA;IACHC,KAAO,EAAAP,IAAA;IACPQ,MAAQ,EAAAR,IAAA;IACRS,MAAQ,EAAAV,KAAA;IACRE,WAAA;IACAS,SAAA,EAAW,iBAAiBpB,WAAA,CAAYK,QAAQ;IAChD,GAAGQ;EACL,GACA,CACE,GAAGP,QAAS,CAAAe,GAAA,CAAI,CAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,KAAAR,aAAA,CAAcO,GAAK,EAAAC,KAAK,CAAC,GAC3D,KACGC,KAAA,CAAMC,OAAQ,CAAAb,QAAQ,IAAIA,QAAW,IAACA,QAAQ,MAAM,EAAC,EAG5D,EACJ;EAEAL,SAAA,CAAUmB,WAAA,GAAc,GAAGrB,QAAA;EAEpB,OAAAE,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}