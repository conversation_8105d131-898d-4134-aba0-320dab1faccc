{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SmartphoneNfc = createLucideIcon(\"SmartphoneNfc\", [[\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"7\",\n  height: \"12\",\n  rx: \"1\",\n  key: \"wbc15s\"\n}], [\"path\", {\n  d: \"M13 8.32a7.43 7.43 0 0 1 0 7.36\",\n  key: \"1g306n\"\n}], [\"path\", {\n  d: \"M16.46 6.21a11.76 11.76 0 0 1 0 11.58\",\n  key: \"uqvjvo\"\n}], [\"path\", {\n  d: \"M19.91 4.1a15.91 15.91 0 0 1 .01 15.8\",\n  key: \"ujntz3\"\n}]]);\nexport { SmartphoneNfc as default };", "map": {"version": 3, "names": ["SmartphoneNfc", "createLucideIcon", "x", "y", "width", "height", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\smartphone-nfc.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst SmartphoneNfc = createLucideIcon('SmartphoneNfc', [\n  [\n    'rect',\n    { x: '2', y: '6', width: '7', height: '12', rx: '1', key: 'wbc15s' },\n  ],\n  ['path', { d: 'M13 8.32a7.43 7.43 0 0 1 0 7.36', key: '1g306n' }],\n  ['path', { d: 'M16.46 6.21a11.76 11.76 0 0 1 0 11.58', key: 'uqvjvo' }],\n  ['path', { d: 'M19.91 4.1a15.91 15.91 0 0 1 .01 15.8', key: 'ujntz3' }],\n]);\n\nexport default SmartphoneNfc;\n"], "mappings": ";;;;;AAEM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EAAEC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}