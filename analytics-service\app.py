from flask import Flask, request, jsonify
from flask_cors import CORS
import pika
import json
import os
import threading
import time
from pymongo import MongoClient
from bson.objectid import ObjectId
from datetime import datetime, timedelta
import logging
import pandas as pd
import numpy as np
from io import BytesIO
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Environment variables
MONGODB_URI = os.environ.get('MONGODB_URI', 'mongodb://localhost:27017/analytics-service')
RABBITMQ_URL = os.environ.get('RABBITMQ_URL', 'amqp://localhost')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key')

# Connect to MongoDB
try:
    client = MongoClient(MONGODB_URI)
    db = client.get_database()
    user_analytics = db.user_analytics
    project_analytics = db.project_analytics
    bid_analytics = db.bid_analytics
    platform_analytics = db.platform_analytics
    logger.info("Connected to MongoDB")
except Exception as e:
    logger.error(f"MongoDB connection error: {e}")
    raise

# RabbitMQ connection
connection = None
channel = None

def connect_to_rabbitmq():
    global connection, channel
    try:
        connection = pika.BlockingConnection(pika.URLParameters(RABBITMQ_URL))
        channel = connection.channel()
        
        # Declare exchanges
        channel.exchange_declare(exchange='user_events', exchange_type='topic', durable=True)
        channel.exchange_declare(exchange='project_events', exchange_type='topic', durable=True)
        
        # Declare queues
        channel.queue_declare(queue='analytics-service-user-events', durable=True)
        channel.queue_declare(queue='analytics-service-project-events', durable=True)
        
        # Bind queues to exchanges
        channel.queue_bind(exchange='user_events', queue='analytics-service-user-events', routing_key='user.#')
        channel.queue_bind(exchange='project_events', queue='analytics-service-project-events', routing_key='project.#')
        channel.queue_bind(exchange='project_events', queue='analytics-service-project-events', routing_key='bid.#')
        
        logger.info("Connected to RabbitMQ")
        return True
    except Exception as e:
        logger.error(f"RabbitMQ connection error: {e}")
        return False

# Message consumer
def consume_messages():
    global channel
    
    def callback(ch, method, properties, body):
        try:
            message = json.loads(body)
            routing_key = method.routing_key
            logger.info(f"Received message with routing key {routing_key}: {message}")
            
            # Process message based on routing key
            if routing_key == 'user.created':
                handle_user_created(message)
            elif routing_key == 'user.login':
                handle_user_login(message)
            elif routing_key == 'project.created':
                handle_project_created(message)
            elif routing_key == 'bid.created':
                handle_bid_created(message)
            elif routing_key == 'bid.accepted':
                handle_bid_accepted(message)
            
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
    
    # Start consuming messages
    channel.basic_consume(queue='analytics-service-user-events', on_message_callback=callback)
    channel.basic_consume(queue='analytics-service-project-events', on_message_callback=callback)
    
    try:
        logger.info("Starting to consume messages")
        channel.start_consuming()
    except Exception as e:
        logger.error(f"Error consuming messages: {e}")

# Event handlers
def handle_user_created(message):
    user_id = message.get('userId')
    role = message.get('role')
    
    # Record new user registration
    user_analytics.insert_one({
        'userId': user_id,
        'role': role,
        'event': 'registration',
        'timestamp': datetime.now()
    })
    
    # Update platform analytics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    platform_analytics.update_one(
        {'date': today, 'metric': f'{role}_registrations'},
        {'$inc': {'count': 1}},
        upsert=True
    )
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'total_registrations'},
        {'$inc': {'count': 1}},
        upsert=True
    )

def handle_user_login(message):
    user_id = message.get('userId')
    timestamp = message.get('timestamp')
    
    # Record user login
    user_analytics.insert_one({
        'userId': user_id,
        'event': 'login',
        'timestamp': datetime.now()
    })
    
    # Update platform analytics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'logins'},
        {'$inc': {'count': 1}},
        upsert=True
    )

def handle_project_created(message):
    project_id = message.get('projectId')
    client_id = message.get('clientId')
    category = message.get('category')
    skills = message.get('skills', [])
    
    # Record new project
    project_analytics.insert_one({
        'projectId': project_id,
        'clientId': client_id,
        'category': category,
        'skills': skills,
        'event': 'creation',
        'timestamp': datetime.now()
    })
    
    # Update platform analytics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'projects_created'},
        {'$inc': {'count': 1}},
        upsert=True
    )
    
    platform_analytics.update_one(
        {'date': today, 'metric': f'category_{category}'},
        {'$inc': {'count': 1}},
        upsert=True
    )
    
    # Update skills analytics
    for skill in skills:
        platform_analytics.update_one(
            {'date': today, 'metric': f'skill_{skill}'},
            {'$inc': {'count': 1}},
            upsert=True
        )

def handle_bid_created(message):
    bid_id = message.get('bidId')
    project_id = message.get('projectId')
    freelancer_id = message.get('freelancerId')
    amount = message.get('amount')
    
    # Record new bid
    bid_analytics.insert_one({
        'bidId': bid_id,
        'projectId': project_id,
        'freelancerId': freelancer_id,
        'amount': amount,
        'event': 'creation',
        'timestamp': datetime.now()
    })
    
    # Update platform analytics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'bids_created'},
        {'$inc': {'count': 1}},
        upsert=True
    )
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'bid_amount_total'},
        {'$inc': {'count': amount}},
        upsert=True
    )

def handle_bid_accepted(message):
    bid_id = message.get('bidId')
    project_id = message.get('projectId')
    freelancer_id = message.get('freelancerId')
    
    # Record bid acceptance
    bid_analytics.update_one(
        {'bidId': bid_id},
        {'$set': {'event': 'accepted', 'acceptedAt': datetime.now()}}
    )
    
    # Update platform analytics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    platform_analytics.update_one(
        {'date': today, 'metric': 'bids_accepted'},
        {'$inc': {'count': 1}},
        upsert=True
    )
    
    # Get bid amount
    bid = bid_analytics.find_one({'bidId': bid_id})
    if bid and 'amount' in bid:
        platform_analytics.update_one(
            {'date': today, 'metric': 'revenue_potential'},
            {'$inc': {'count': bid['amount'] * 0.1}},  # Assuming 10% platform fee
            upsert=True
        )

# API Routes
@app.route('/api/analytics/platform', methods=['GET'])
def get_platform_analytics():
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    metrics = request.args.get('metrics', '').split(',') if request.args.get('metrics') else []
    
    # Parse dates
    try:
        if start_date:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        else:
            start_date = datetime.now() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        else:
            end_date = datetime.now()
    except ValueError:
        return jsonify({'error': 'Invalid date format'}), 400
    
    # Build query
    query = {
        'date': {
            '$gte': start_date.replace(hour=0, minute=0, second=0, microsecond=0),
            '$lte': end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        }
    }
    
    if metrics:
        query['metric'] = {'$in': metrics}
    
    # Get analytics data
    analytics_data = list(platform_analytics.find(query).sort('date', 1))
    
    # Process data for response
    result = {}
    for item in analytics_data:
        metric = item['metric']
        date_str = item['date'].strftime('%Y-%m-%d')
        
        if metric not in result:
            result[metric] = []
        
        result[metric].append({
            'date': date_str,
            'value': item['count']
        })
    
    return jsonify(result)

@app.route('/api/analytics/user', methods=['GET'])
def get_user_analytics():
    user_id = request.args.get('userId')
    
    if not user_id:
        return jsonify({'error': 'User ID is required'}), 400
    
    # Get user activity
    user_activity = list(user_analytics.find({'userId': user_id}).sort('timestamp', -1))
    
    # Convert ObjectId to string for JSON serialization
    for item in user_activity:
        item['_id'] = str(item['_id'])
        if 'timestamp' in item:
            item['timestamp'] = item['timestamp'].isoformat()
    
    # Get user projects (if client)
    projects = list(project_analytics.find({'clientId': user_id}).sort('timestamp', -1))
    
    # Convert ObjectId to string for JSON serialization
    for item in projects:
        item['_id'] = str(item['_id'])
        if 'timestamp' in item:
            item['timestamp'] = item['timestamp'].isoformat()
    
    # Get user bids (if freelancer)
    bids = list(bid_analytics.find({'freelancerId': user_id}).sort('timestamp', -1))
    
    # Convert ObjectId to string for JSON serialization
    for item in bids:
        item['_id'] = str(item['_id'])
        if 'timestamp' in item:
            item['timestamp'] = item['timestamp'].isoformat()
    
    return jsonify({
        'activity': user_activity,
        'projects': projects,
        'bids': bids
    })

@app.route('/api/analytics/project', methods=['GET'])
def get_project_analytics():
    project_id = request.args.get('projectId')
    
    if not project_id:
        return jsonify({'error': 'Project ID is required'}), 400
    
    # Get project data
    project_data = project_analytics.find_one({'projectId': project_id})
    
    if not project_data:
        return jsonify({'error': 'Project not found'}), 404
    
    # Convert ObjectId to string for JSON serialization
    project_data['_id'] = str(project_data['_id'])
    if 'timestamp' in project_data:
        project_data['timestamp'] = project_data['timestamp'].isoformat()
    
    # Get bids for the project
    bids = list(bid_analytics.find({'projectId': project_id}).sort('timestamp', -1))
    
    # Convert ObjectId to string for JSON serialization
    for item in bids:
        item['_id'] = str(item['_id'])
        if 'timestamp' in item:
            item['timestamp'] = item['timestamp'].isoformat()
    
    # Calculate bid statistics
    bid_amounts = [bid['amount'] for bid in bids if 'amount' in bid]
    bid_stats = {
        'count': len(bids),
        'min': min(bid_amounts) if bid_amounts else 0,
        'max': max(bid_amounts) if bid_amounts else 0,
        'avg': sum(bid_amounts) / len(bid_amounts) if bid_amounts else 0
    }
    
    return jsonify({
        'project': project_data,
        'bids': bids,
        'bidStats': bid_stats
    })

@app.route('/api/analytics/reports/user-growth', methods=['GET'])
def get_user_growth_report():
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    
    # Parse dates
    try:
        if start_date:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        else:
            start_date = datetime.now() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        else:
            end_date = datetime.now()
    except ValueError:
        return jsonify({'error': 'Invalid date format'}), 400
    
    # Get user registrations by day
    pipeline = [
        {
            '$match': {
                'event': 'registration',
                'timestamp': {
                    '$gte': start_date,
                    '$lte': end_date
                }
            }
        },
        {
            '$group': {
                '_id': {
                    'date': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$timestamp'}},
                    'role': '$role'
                },
                'count': {'$sum': 1}
            }
        },
        {
            '$sort': {'_id.date': 1}
        }
    ]
    
    registrations = list(user_analytics.aggregate(pipeline))
    
    # Process data for response
    result = {
        'dates': [],
        'clients': [],
        'freelancers': [],
        'admins': []
    }
    
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        result['dates'].append(date_str)
        
        client_count = 0
        freelancer_count = 0
        admin_count = 0
        
        for reg in registrations:
            if reg['_id']['date'] == date_str:
                if reg['_id']['role'] == 'client':
                    client_count = reg['count']
                elif reg['_id']['role'] == 'freelancer':
                    freelancer_count = reg['count']
                elif reg['_id']['role'] == 'admin':
                    admin_count = reg['count']
        
        result['clients'].append(client_count)
        result['freelancers'].append(freelancer_count)
        result['admins'].append(admin_count)
        
        current_date += timedelta(days=1)
    
    return jsonify(result)

@app.route('/api/analytics/reports/project-categories', methods=['GET'])
def get_project_categories_report():
    # Get project count by category
    pipeline = [
        {
            '$match': {
                'event': 'creation'
            }
        },
        {
            '$group': {
                '_id': '$category',
                'count': {'$sum': 1}
            }
        },
        {
            '$sort': {'count': -1}
        }
    ]
    
    categories = list(project_analytics.aggregate(pipeline))
    
    # Process data for response
    result = {
        'categories': [],
        'counts': []
    }
    
    for category in categories:
        if category['_id']:
            result['categories'].append(category['_id'])
            result['counts'].append(category['count'])
    
    return jsonify(result)

@app.route('/api/analytics/reports/popular-skills', methods=['GET'])
def get_popular_skills_report():
    # Get project count by skill
    pipeline = [
        {
            '$match': {
                'event': 'creation'
            }
        },
        {
            '$unwind': '$skills'
        },
        {
            '$group': {
                '_id': '$skills',
                'count': {'$sum': 1}
            }
        },
        {
            '$sort': {'count': -1}
        },
        {
            '$limit': 10
        }
    ]
    
    skills = list(project_analytics.aggregate(pipeline))
    
    # Process data for response
    result = {
        'skills': [],
        'counts': []
    }
    
    for skill in skills:
        if skill['_id']:
            result['skills'].append(skill['_id'])
            result['counts'].append(skill['count'])
    
    return jsonify(result)

@app.route('/api/analytics/reports/bid-statistics', methods=['GET'])
def get_bid_statistics_report():
    # Get bid statistics
    pipeline = [
        {
            '$match': {
                'event': 'creation'
            }
        },
        {
            '$group': {
                '_id': None,
                'avgAmount': {'$avg': '$amount'},
                'minAmount': {'$min': '$amount'},
                'maxAmount': {'$max': '$amount'},
                'totalBids': {'$sum': 1}
            }
        }
    ]
    
    stats = list(bid_analytics.aggregate(pipeline))
    
    if not stats:
        return jsonify({
            'avgAmount': 0,
            'minAmount': 0,
            'maxAmount': 0,
            'totalBids': 0
        })
    
    # Get bid distribution
    pipeline = [
        {
            '$match': {
                'event': 'creation'
            }
        },
        {
            '$bucket': {
                'groupBy': '$amount',
                'boundaries': [0, 100, 500, 1000, 5000, 10000, 50000],
                'default': 'Other',
                'output': {
                    'count': {'$sum': 1}
                }
            }
        }
    ]
    
    distribution = list(bid_analytics.aggregate(pipeline))
    
    # Process distribution data
    ranges = ['$0-$100', '$100-$500', '$500-$1000', '$1000-$5000', '$5000-$10000', '$10000-$50000', 'Other']
    counts = [0] * len(ranges)
    
    for i, d in enumerate(distribution):
        if i < len(counts):
            counts[i] = d['count']
    
    return jsonify({
        'avgAmount': stats[0]['avgAmount'],
        'minAmount': stats[0]['minAmount'],
        'maxAmount': stats[0]['maxAmount'],
        'totalBids': stats[0]['totalBids'],
        'distribution': {
            'ranges': ranges,
            'counts': counts
        }
    })

@app.route('/api/analytics/reports/export', methods=['GET'])
def export_analytics_report():
    report_type = request.args.get('type', 'csv')
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    
    # Parse dates
    try:
        if start_date:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        else:
            start_date = datetime.now() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        else:
            end_date = datetime.now()
    except ValueError:
        return jsonify({'error': 'Invalid date format'}), 400
    
    # Get platform analytics data
    query = {
        'date': {
            '$gte': start_date.replace(hour=0, minute=0, second=0, microsecond=0),
            '$lte': end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        }
    }
    
    analytics_data = list(platform_analytics.find(query).sort('date', 1))
    
    # Convert to DataFrame
    data = []
    for item in analytics_data:
        data.append({
            'date': item['date'].strftime('%Y-%m-%d'),
            'metric': item['metric'],
            'value': item['count']
        })
    
    df = pd.DataFrame(data)
    
    # Pivot the data for better readability
    if not df.empty:
        df_pivot = df.pivot(index='date', columns='metric', values='value').fillna(0)
    else:
        df_pivot = pd.DataFrame()
    
    # Export based on requested format
    if report_type == 'csv':
        output = BytesIO()
        df_pivot.to_csv(output)
        output.seek(0)
        
        return output.getvalue(), 200, {
            'Content-Type': 'text/csv',
            'Content-Disposition': f'attachment; filename=analytics_report_{datetime.now().strftime("%Y%m%d")}.csv'
        }
    elif report_type == 'json':
        return jsonify(df_pivot.reset_index().to_dict(orient='records'))
    else:
        return jsonify({'error': 'Unsupported export format'}), 400

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'OK',
        'service': 'Analytics Service'
    })

# Start RabbitMQ consumer in a separate thread
def start_consumer():
    if connect_to_rabbitmq():
        consumer_thread = threading.Thread(target=consume_messages)
        consumer_thread.daemon = True
        consumer_thread.start()

# Start the consumer when the application starts
# @app.before_first_request  # Deprecated in Flask 2.2+
def before_first_request():
    start_consumer()

# Retry RabbitMQ connection if it fails
def retry_rabbitmq_connection():
    while not connect_to_rabbitmq():
        logger.info("Retrying RabbitMQ connection in 5 seconds...")
        time.sleep(5)
    
    consumer_thread = threading.Thread(target=consume_messages)
    consumer_thread.daemon = True
    consumer_thread.start()

# Start the retry thread
retry_thread = threading.Thread(target=retry_rabbitmq_connection)
retry_thread.daemon = True
retry_thread.start()

if __name__ == '__main__':
    # Start the consumer when the application starts
    before_first_request()
    app.run(host='0.0.0.0', port=7000)
