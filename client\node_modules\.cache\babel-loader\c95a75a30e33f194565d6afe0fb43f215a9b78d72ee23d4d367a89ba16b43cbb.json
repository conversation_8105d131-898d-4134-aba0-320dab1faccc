{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UserX = createLucideIcon(\"UserX\", [[\"path\", {\n  d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n  key: \"1yyitq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}], [\"line\", {\n  x1: \"17\",\n  y1: \"8\",\n  x2: \"22\",\n  y2: \"13\",\n  key: \"10apcb\"\n}], [\"line\", {\n  x1: \"22\",\n  y1: \"8\",\n  x2: \"17\",\n  y2: \"13\",\n  key: \"1l8di5\"\n}]]);\nexport { UserX as default };", "map": {"version": 3, "names": ["UserX", "createLucideIcon", "d", "key", "cx", "cy", "r", "x1", "y1", "x2", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\user-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst UserX = createLucideIcon('UserX', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['line', { x1: '17', y1: '8', x2: '22', y2: '13', key: '10apcb' }],\n  ['line', { x1: '22', y1: '8', x2: '17', y2: '13', key: '1l8di5' }],\n]);\n\nexport default UserX;\n"], "mappings": ";;;;;AAEM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAS,GACrD,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAP,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAP,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}