{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst XSquare = createLucideIcon(\"XSquare\", [[\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"maln0c\"\n}], [\"line\", {\n  x1: \"9\",\n  y1: \"9\",\n  x2: \"15\",\n  y2: \"15\",\n  key: \"10u9bu\"\n}], [\"line\", {\n  x1: \"15\",\n  y1: \"9\",\n  x2: \"9\",\n  y2: \"15\",\n  key: \"19zs77\"\n}]]);\nexport { XSquare as default };", "map": {"version": 3, "names": ["XSquare", "createLucideIcon", "x", "y", "width", "height", "rx", "ry", "key", "x1", "y1", "x2", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\x-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst XSquare = createLucideIcon('XSquare', [\n  [\n    'rect',\n    {\n      x: '3',\n      y: '3',\n      width: '18',\n      height: '18',\n      rx: '2',\n      ry: '2',\n      key: 'maln0c',\n    },\n  ],\n  ['line', { x1: '9', y1: '9', x2: '15', y2: '15', key: '10u9bu' }],\n  ['line', { x1: '15', y1: '9', x2: '9', y2: '15', key: '19zs77' }],\n]);\n\nexport default XSquare;\n"], "mappings": ";;;;;AAEM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,CAAG;EACHC,KAAO;EACPC,MAAQ;EACRC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}