{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst X = createLucideIcon(\"X\", [[\"line\", {\n  x1: \"18\",\n  y1: \"6\",\n  x2: \"6\",\n  y2: \"18\",\n  key: \"1o5bob\"\n}], [\"line\", {\n  x1: \"6\",\n  y1: \"6\",\n  x2: \"18\",\n  y2: \"18\",\n  key: \"z4dcbv\"\n}]]);\nexport { X as default };", "map": {"version": 3, "names": ["X", "createLucideIcon", "x1", "y1", "x2", "y2", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst X = createLucideIcon('X', [\n  ['line', { x1: '18', y1: '6', x2: '6', y2: '18', key: '1o5bob' }],\n  ['line', { x1: '6', y1: '6', x2: '18', y2: '18', key: 'z4dcbv' }],\n]);\n\nexport default X;\n"], "mappings": ";;;;;AAEM,MAAAA,CAAA,GAAIC,gBAAA,CAAiB,GAAK,GAC9B,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}