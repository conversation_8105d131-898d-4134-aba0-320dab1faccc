{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ThumbsUp = createLucideIcon(\"ThumbsUp\", [[\"path\", {\n  d: \"M7 10v12\",\n  key: \"1qc93n\"\n}], [\"path\", {\n  d: \"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z\",\n  key: \"y3tblf\"\n}]]);\nexport { ThumbsUp as default };", "map": {"version": 3, "names": ["ThumbsUp", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\thumbs-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst ThumbsUp = createLucideIcon('ThumbsUp', [\n  ['path', { d: 'M7 10v12', key: '1qc93n' }],\n  [\n    'path',\n    {\n      d: 'M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z',\n      key: 'y3tblf',\n    },\n  ],\n]);\n\nexport default ThumbsUp;\n"], "mappings": ";;;;;AAEM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}