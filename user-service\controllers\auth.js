const User = require("../models/User")
const jwt = require("jsonwebtoken")
const crypto = require("crypto")
const { validationResult } = require("express-validator")
const { channel } = require("../server")

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "1d"

// Register a new user
exports.register = async (req, res) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ error: errors.array()[0].msg })
  }

  const { email, password, role, phone, name } = req.body

  try {
    // Check if user already exists
    let user = await User.findOne({ email })
    if (user) {
      return res.status(400).json({ error: "User already exists" })
    }

    // Create verification token
    const verificationToken = crypto.randomBytes(20).toString("hex")
    const verificationExpires = Date.now() + 24 * 60 * 60 * 1000 // 24 hours

    // Create new user
    user = new User({
      email,
      password,
      role,
      phone,
      name,
      verificationToken,
      verificationExpires,
    })

    await user.save()

    // Publish user created event
    if (channel) {
      channel.publish(
        "user_events",
        "user.created",
        Buffer.from(
          JSON.stringify({
            userId: user._id,
            email: user.email,
            role: user.role,
            verificationToken,
          }),
        ),
      )
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user._id, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })

    res.status(201).json({
      message: "User registered successfully",
      token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
      },
    })
  } catch (error) {
    console.error("Registration error:", error)
    res.status(500).json({ error: "Server error" })
  }
}

// Login user
exports.login = async (req, res) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() })
  }

  const { email, password } = req.body

  try {
    // Check if user exists
    const user = await User.findOne({ email })
    if (!user) {
      return res.status(400).json({ message: "Invalid credentials" })
    }

    // Check password
    const isMatch = await user.comparePassword(password)
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" })
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user._id, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })

    // Publish login event
    if (channel) {
      channel.publish(
        "user_events",
        "user.login",
        Buffer.from(
          JSON.stringify({
            userId: user._id,
            timestamp: new Date(),
          }),
        ),
      )
    }

    res.json({
      message: "Login successful",
      token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ message: "Server error" })
  }
}

// Verify email
exports.verifyEmail = async (req, res) => {
  const { token } = req.params

  try {
    const user = await User.findOne({
      verificationToken: token,
      verificationExpires: { $gt: Date.now() },
    })

    if (!user) {
      return res.status(400).json({ message: "Invalid or expired verification token" })
    }

    user.isVerified = true
    user.verificationToken = undefined
    user.verificationExpires = undefined
    await user.save()

    // Publish verification event
    if (channel) {
      channel.publish(
        "user_events",
        "user.verified",
        Buffer.from(
          JSON.stringify({
            userId: user._id,
            email: user.email,
          }),
        ),
      )
    }

    res.json({ message: "Email verified successfully" })
  } catch (error) {
    console.error("Verification error:", error)
    res.status(500).json({ message: "Server error" })
  }
}

// Request password reset
exports.forgotPassword = async (req, res) => {
  const { email } = req.body

  try {
    const user = await User.findOne({ email })
    if (!user) {
      return res.status(404).json({ message: "User not found" })
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(20).toString("hex")
    user.resetPasswordToken = resetToken
    user.resetPasswordExpires = Date.now() + 3600000 // 1 hour
    await user.save()

    // Publish password reset request event
    if (channel) {
      channel.publish(
        "user_events",
        "user.password.reset.request",
        Buffer.from(
          JSON.stringify({
            userId: user._id,
            email: user.email,
            resetToken,
          }),
        ),
      )
    }

    res.json({ message: "Password reset email sent" })
  } catch (error) {
    console.error("Forgot password error:", error)
    res.status(500).json({ message: "Server error" })
  }
}

// Reset password
exports.resetPassword = async (req, res) => {
  const { token } = req.params
  const { password } = req.body

  try {
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    })

    if (!user) {
      return res.status(400).json({ message: "Invalid or expired reset token" })
    }

    user.password = password
    user.resetPasswordToken = undefined
    user.resetPasswordExpires = undefined
    await user.save()

    // Publish password reset success event
    if (channel) {
      channel.publish(
        "user_events",
        "user.password.reset.success",
        Buffer.from(
          JSON.stringify({
            userId: user._id,
            email: user.email,
          }),
        ),
      )
    }

    res.json({ message: "Password reset successful" })
  } catch (error) {
    console.error("Reset password error:", error)
    res.status(500).json({ message: "Server error" })
  }
}

// Get current user
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select("-password")

    if (!user) {
      return res.status(404).json({ message: "User not found" })
    }

    res.json({
      success: true,
      data: {
        id: user._id,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
      },
    })
  } catch (error) {
    console.error("Get current user error:", error)
    res.status(500).json({ message: "Server error" })
  }
}

// Logout user
exports.logout = (req, res) => {
  // JWT is stateless, so we don't need to do anything server-side
  // The client will remove the token from local storage
  res.json({ success: true, message: "Logged out successfully" })
}

// Update user details
exports.updateDetails = async (req, res) => {
  try {
    const { name, email } = req.body
    const updateFields = {}

    if (name) updateFields.name = name
    if (email) updateFields.email = email

    const user = await User.findByIdAndUpdate(
      req.user.userId,
      { $set: updateFields },
      { new: true, runValidators: true }
    ).select("-password")

    if (!user) {
      return res.status(404).json({ message: "User not found" })
    }

    res.json({
      success: true,
      data: {
        id: user._id,
        email: user.email,
        role: user.role,
        name: user.name,
        isVerified: user.isVerified,
      },
    })
  } catch (error) {
    console.error("Update details error:", error)
    res.status(500).json({ message: "Server error" })
  }
}
