{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst WrapText = createLucideIcon(\"WrapText\", [[\"line\", {\n  x1: \"3\",\n  y1: \"6\",\n  x2: \"21\",\n  y2: \"6\",\n  key: \"1tp2lp\"\n}], [\"path\", {\n  d: \"M3 12h15a3 3 0 1 1 0 6h-4\",\n  key: \"1cl7v7\"\n}], [\"polyline\", {\n  points: \"16 16 14 18 16 20\",\n  key: \"1jznyi\"\n}], [\"line\", {\n  x1: \"3\",\n  y1: \"18\",\n  x2: \"10\",\n  y2: \"18\",\n  key: \"16bh46\"\n}]]);\nexport { WrapText as default };", "map": {"version": 3, "names": ["WrapText", "createLucideIcon", "x1", "y1", "x2", "y2", "key", "d", "points"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\wrap-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst WrapText = createLucideIcon('WrapText', [\n  ['line', { x1: '3', y1: '6', x2: '21', y2: '6', key: '1tp2lp' }],\n  ['path', { d: 'M3 12h15a3 3 0 1 1 0 6h-4', key: '1cl7v7' }],\n  ['polyline', { points: '16 16 14 18 16 20', key: '1jznyi' }],\n  ['line', { x1: '3', y1: '18', x2: '10', y2: '18', key: '16bh46' }],\n]);\n\nexport default WrapText;\n"], "mappings": ";;;;;AAEM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAEE,MAAA,EAAQ,mBAAqB;EAAAF,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}