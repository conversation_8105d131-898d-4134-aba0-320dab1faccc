[{"C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\index.jsx": "1", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\App.jsx": "3", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\SocketContext.jsx": "4", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\AuthContext.jsx": "5", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\HomePage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\NotificationContext.jsx": "7", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\NotFoundPage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\LoginPage.jsx": "9", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\RegisterPage.jsx": "10", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx": "11", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\OAuthCallbackPage.jsx": "12", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\ResetPasswordPage.jsx": "13", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\BrowseFreelancersPage.jsx": "14", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientDashboardPage.jsx": "15", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientMessagesPage.jsx": "16", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\PostProjectPage.jsx": "17", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientProjectsPage.jsx": "18", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\EditProjectPage.jsx": "19", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProfilePage.jsx": "20", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\BrowseProjectsPage.jsx": "21", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerDashboardPage.jsx": "22", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProjectsPage.jsx": "23", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerBidsPage.jsx": "24", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProfileEditPage.jsx": "25", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerMessagesPage.jsx": "26", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\resources\\ResourceDetailPage.jsx": "27", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\resources\\ResourcesPage.jsx": "28", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationSettingsPage.jsx": "29", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminDashboardPage.jsx": "30", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUserDetailPage.jsx": "31", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationsPage.jsx": "32", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUsersPage.jsx": "33", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationDetailPage.jsx": "34", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUserEditPage.jsx": "35", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\projects\\ProjectDetailPage.jsx": "36", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\notifications\\NotificationsPage.jsx": "37", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\layouts\\MainLayout.jsx": "38", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\layouts\\DashboardLayout.jsx": "39", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\ProtectedRoute.jsx": "40", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Alert.jsx": "41", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Badge.jsx": "42", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Spinner.jsx": "43", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Card.jsx": "44", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Button.jsx": "45", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\utils\\api.jsx": "46", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\freelancers\\FreelancerCard.jsx": "47", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\projects\\ProjectCard.jsx": "48", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\charts\\ChartComponent.jsx": "49", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\projects\\BidForm.jsx": "50", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Sidebar.jsx": "51", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Footer.jsx": "52", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\DashboardHeader.jsx": "53", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Navbar.jsx": "54", "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\notifications\\NotificationDropdown.jsx": "55"}, {"size": 545, "mtime": 1749806352769, "results": "56", "hashOfConfig": "57"}, {"size": 395, "mtime": 1749806352778, "results": "58", "hashOfConfig": "57"}, {"size": 7370, "mtime": 1749806352745, "results": "59", "hashOfConfig": "57"}, {"size": 1367, "mtime": 1749806353147, "results": "60", "hashOfConfig": "57"}, {"size": 6078, "mtime": 1749806353124, "results": "61", "hashOfConfig": "57"}, {"size": 20407, "mtime": 1749806353170, "results": "62", "hashOfConfig": "57"}, {"size": 3054, "mtime": 1749806353136, "results": "63", "hashOfConfig": "57"}, {"size": 540, "mtime": 1749806353180, "results": "64", "hashOfConfig": "57"}, {"size": 9122, "mtime": 1749930623356, "results": "65", "hashOfConfig": "57"}, {"size": 7023, "mtime": 1749806353328, "results": "66", "hashOfConfig": "57"}, {"size": 5485, "mtime": 1749806353295, "results": "67", "hashOfConfig": "57"}, {"size": 3269, "mtime": 1749806353317, "results": "68", "hashOfConfig": "57"}, {"size": 7689, "mtime": 1749806353339, "results": "69", "hashOfConfig": "57"}, {"size": 9350, "mtime": 1749806353364, "results": "70", "hashOfConfig": "57"}, {"size": 15083, "mtime": 1749806353373, "results": "71", "hashOfConfig": "57"}, {"size": 16609, "mtime": 1749806353386, "results": "72", "hashOfConfig": "57"}, {"size": 13168, "mtime": 1749806353416, "results": "73", "hashOfConfig": "57"}, {"size": 15324, "mtime": 1749806353395, "results": "74", "hashOfConfig": "57"}, {"size": 13982, "mtime": 1749806353407, "results": "75", "hashOfConfig": "57"}, {"size": 13974, "mtime": 1749806353497, "results": "76", "hashOfConfig": "57"}, {"size": 10577, "mtime": 1749806353442, "results": "77", "hashOfConfig": "57"}, {"size": 27646, "mtime": 1749806353465, "results": "78", "hashOfConfig": "57"}, {"size": 9529, "mtime": 1749806353505, "results": "79", "hashOfConfig": "57"}, {"size": 8461, "mtime": 1749806353453, "results": "80", "hashOfConfig": "57"}, {"size": 18834, "mtime": 1749806353487, "results": "81", "hashOfConfig": "57"}, {"size": 22360, "mtime": 1749806353477, "results": "82", "hashOfConfig": "57"}, {"size": 12129, "mtime": 1749806353581, "results": "83", "hashOfConfig": "57"}, {"size": 11091, "mtime": 1749806353591, "results": "84", "hashOfConfig": "57"}, {"size": 13454, "mtime": 1749806353252, "results": "85", "hashOfConfig": "57"}, {"size": 29288, "mtime": 1749806353205, "results": "86", "hashOfConfig": "57"}, {"size": 9947, "mtime": 1749806353213, "results": "87", "hashOfConfig": "57"}, {"size": 10433, "mtime": 1749806353262, "results": "88", "hashOfConfig": "57"}, {"size": 14744, "mtime": 1749806353234, "results": "89", "hashOfConfig": "57"}, {"size": 11412, "mtime": 1749806353242, "results": "90", "hashOfConfig": "57"}, {"size": 6867, "mtime": 1749806353223, "results": "91", "hashOfConfig": "57"}, {"size": 22867, "mtime": 1749806353556, "results": "92", "hashOfConfig": "57"}, {"size": 14989, "mtime": 1749806353530, "results": "93", "hashOfConfig": "57"}, {"size": 1137, "mtime": 1749806352983, "results": "94", "hashOfConfig": "57"}, {"size": 4405, "mtime": 1749806352974, "results": "95", "hashOfConfig": "57"}, {"size": 1088, "mtime": 1749806352914, "results": "96", "hashOfConfig": "57"}, {"size": 2326, "mtime": 1749806352878, "results": "97", "hashOfConfig": "57"}, {"size": 899, "mtime": 1749806352886, "results": "98", "hashOfConfig": "57"}, {"size": 472, "mtime": 1749806352925, "results": "99", "hashOfConfig": "57"}, {"size": 881, "mtime": 1749806352904, "results": "100", "hashOfConfig": "57"}, {"size": 2025, "mtime": 1749806352895, "results": "101", "hashOfConfig": "57"}, {"size": 1785, "mtime": 1749806353639, "results": "102", "hashOfConfig": "57"}, {"size": 3714, "mtime": 1749806352949, "results": "103", "hashOfConfig": "57"}, {"size": 5335, "mtime": 1749806353088, "results": "104", "hashOfConfig": "57"}, {"size": 2815, "mtime": 1749806352808, "results": "105", "hashOfConfig": "57"}, {"size": 5244, "mtime": 1749806353076, "results": "106", "hashOfConfig": "57"}, {"size": 14377, "mtime": 1749806353031, "results": "107", "hashOfConfig": "57"}, {"size": 14627, "mtime": 1749806353013, "results": "108", "hashOfConfig": "57"}, {"size": 15643, "mtime": 1749806353003, "results": "109", "hashOfConfig": "57"}, {"size": 10075, "mtime": 1749806353021, "results": "110", "hashOfConfig": "57"}, {"size": 8837, "mtime": 1749806353051, "results": "111", "hashOfConfig": "57"}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4v5iwr", {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\SocketContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\HomePage.jsx", ["277"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\contexts\\NotificationContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\NotFoundPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\LoginPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\RegisterPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\OAuthCallbackPage.jsx", ["278"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\auth\\ResetPasswordPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\BrowseFreelancersPage.jsx", ["279"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientDashboardPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientMessagesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\PostProjectPage.jsx", ["280"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\ClientProjectsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\client\\EditProjectPage.jsx", ["281"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProfilePage.jsx", ["282"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\BrowseProjectsPage.jsx", ["283"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerDashboardPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProjectsPage.jsx", ["284"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerBidsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerProfileEditPage.jsx", ["285"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\freelancer\\FreelancerMessagesPage.jsx", ["286", "287", "288", "289"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\resources\\ResourceDetailPage.jsx", ["290"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\resources\\ResourcesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationSettingsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminDashboardPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUserDetailPage.jsx", [], ["291"], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUsersPage.jsx", ["292", "293"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminVerificationDetailPage.jsx", [], ["294"], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\admin\\AdminUserEditPage.jsx", [], ["295"], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\projects\\ProjectDetailPage.jsx", [], ["296"], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\pages\\notifications\\NotificationsPage.jsx", ["297", "298"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\layouts\\MainLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\layouts\\DashboardLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Alert.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Badge.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Spinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Card.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\common\\Button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\utils\\api.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\freelancers\\FreelancerCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\projects\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\charts\\ChartComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\projects\\BidForm.jsx", ["299"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\DashboardHeader.jsx", ["300"], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\navigation\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\src\\components\\notifications\\NotificationDropdown.jsx", [], [], {"ruleId": "301", "severity": 1, "message": "302", "line": 45, "column": 6, "nodeType": "303", "endLine": 45, "endColumn": 8, "suggestions": "304"}, {"ruleId": "305", "severity": 1, "message": "306", "line": 11, "column": 10, "nodeType": "307", "messageId": "308", "endLine": 11, "endColumn": 17}, {"ruleId": "305", "severity": 1, "message": "309", "line": 22, "column": 27, "nodeType": "307", "messageId": "308", "endLine": 22, "endColumn": 45}, {"ruleId": "305", "severity": 1, "message": "310", "line": 14, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 14, "endColumn": 15}, {"ruleId": "301", "severity": 1, "message": "311", "line": 48, "column": 6, "nodeType": "303", "endLine": 48, "endColumn": 10, "suggestions": "312"}, {"ruleId": "301", "severity": 1, "message": "313", "line": 21, "column": 6, "nodeType": "303", "endLine": 21, "endColumn": 10, "suggestions": "314"}, {"ruleId": "305", "severity": 1, "message": "309", "line": 23, "column": 27, "nodeType": "307", "messageId": "308", "endLine": 23, "endColumn": 45}, {"ruleId": "305", "severity": 1, "message": "310", "line": 14, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 14, "endColumn": 15}, {"ruleId": "305", "severity": 1, "message": "310", "line": 13, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 13, "endColumn": 15}, {"ruleId": "305", "severity": 1, "message": "315", "line": 15, "column": 19, "nodeType": "307", "messageId": "308", "endLine": 15, "endColumn": 28}, {"ruleId": "305", "severity": 1, "message": "316", "line": 16, "column": 9, "nodeType": "307", "messageId": "308", "endLine": 16, "endColumn": 17}, {"ruleId": "305", "severity": 1, "message": "317", "line": 17, "column": 9, "nodeType": "307", "messageId": "308", "endLine": 17, "endColumn": 17}, {"ruleId": "301", "severity": 1, "message": "318", "line": 43, "column": 6, "nodeType": "303", "endLine": 43, "endColumn": 14, "suggestions": "319"}, {"ruleId": "305", "severity": 1, "message": "320", "line": 5, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 5, "endColumn": 21}, {"ruleId": "301", "severity": 1, "message": "321", "line": 25, "column": 6, "nodeType": "303", "endLine": 25, "endColumn": 10, "suggestions": "322", "suppressions": "323"}, {"ruleId": "305", "severity": 1, "message": "310", "line": 14, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 14, "endColumn": 15}, {"ruleId": "301", "severity": 1, "message": "324", "line": 30, "column": 6, "nodeType": "303", "endLine": 30, "endColumn": 51, "suggestions": "325"}, {"ruleId": "301", "severity": 1, "message": "326", "line": 26, "column": 6, "nodeType": "303", "endLine": 26, "endColumn": 10, "suggestions": "327", "suppressions": "328"}, {"ruleId": "301", "severity": 1, "message": "321", "line": 30, "column": 6, "nodeType": "303", "endLine": 30, "endColumn": 10, "suggestions": "329", "suppressions": "330"}, {"ruleId": "301", "severity": 1, "message": "331", "line": 28, "column": 6, "nodeType": "303", "endLine": 28, "endColumn": 10, "suggestions": "332", "suppressions": "333"}, {"ruleId": "305", "severity": 1, "message": "334", "line": 16, "column": 11, "nodeType": "307", "messageId": "308", "endLine": 16, "endColumn": 24}, {"ruleId": "301", "severity": 1, "message": "335", "line": 30, "column": 6, "nodeType": "303", "endLine": 30, "endColumn": 27, "suggestions": "336"}, {"ruleId": "305", "severity": 1, "message": "316", "line": 18, "column": 9, "nodeType": "307", "messageId": "308", "endLine": 18, "endColumn": 17}, {"ruleId": "305", "severity": 1, "message": "337", "line": 21, "column": 9, "nodeType": "307", "messageId": "308", "endLine": 21, "endColumn": 22}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'targetStats.clients', 'targetStats.countries', 'targetStats.freelancers', and 'targetStats.projects'. Either include them or remove the dependency array.", "ArrayExpression", ["338"], "no-unused-vars", "'loading' is assigned a value but never used.", "Identifier", "unusedVar", "'setAvailableSkills' is assigned a value but never used.", "'user' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProject'. Either include it or remove the dependency array.", ["339"], "React Hook useEffect has a missing dependency: 'fetchFreelancerProfile'. Either include it or remove the dependency array.", ["340"], "'connected' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'location' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleMessageRead' and 'handleNewMessage'. Either include them or remove the dependency array.", ["341"], "'resourceId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserDetails'. Either include it or remove the dependency array.", ["342"], ["343"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["344"], "React Hook useEffect has a missing dependency: 'fetchVerificationDetails'. Either include it or remove the dependency array.", ["345"], ["346"], ["347"], ["348"], "React Hook useEffect has a missing dependency: 'fetchProjectDetails'. Either include it or remove the dependency array.", ["349"], ["350"], "'notifications' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["351"], "'mobileMenuRef' is assigned a value but never used.", {"desc": "352", "fix": "353"}, {"desc": "354", "fix": "355"}, {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"kind": "362", "justification": "363"}, {"desc": "364", "fix": "365"}, {"desc": "366", "fix": "367"}, {"kind": "362", "justification": "363"}, {"desc": "360", "fix": "368"}, {"kind": "362", "justification": "363"}, {"desc": "369", "fix": "370"}, {"kind": "362", "justification": "363"}, {"desc": "371", "fix": "372"}, "Update the dependencies array to be: [targetStats.clients, targetStats.countries, targetStats.freelancers, targetStats.projects]", {"range": "373", "text": "374"}, "Update the dependencies array to be: [fetchProject, id]", {"range": "375", "text": "376"}, "Update the dependencies array to be: [fetchFreelancerProfile, id]", {"range": "377", "text": "378"}, "Update the dependencies array to be: [handleMessageRead, handleNewMessage, socket]", {"range": "379", "text": "380"}, "Update the dependencies array to be: [fetchUserDetails, id]", {"range": "381", "text": "382"}, "directive", "", "Update the dependencies array to be: [currentPage, fetchUsers, roleFilter, verificationFilter]", {"range": "383", "text": "384"}, "Update the dependencies array to be: [fetchVerificationDetails, id]", {"range": "385", "text": "386"}, {"range": "387", "text": "382"}, "Update the dependencies array to be: [fetchProjectDetails, id]", {"range": "388", "text": "389"}, "Update the dependencies array to be: [currentPage, fetchNotifications, filter]", {"range": "390", "text": "391"}, [1252, 1254], "[targetStats.clients, targetStats.countries, targetStats.freelancers, targetStats.projects]", [1208, 1212], "[fetchProject, id]", [761, 765], "[fetchFreelancerProfile, id]", [1531, 1539], "[handleMessageRead, handleNewMessage, socket]", [935, 939], "[fetchUserDetails, id]", [1213, 1258], "[currentPage, fetchUsers, roleFilter, verificationFilter]", [1032, 1036], "[fetchVerificationDetails, id]", [927, 931], [1115, 1119], "[fetchProjectDetails, id]", [1210, 1231], "[currentPage, fetchNotifications, filter]"]