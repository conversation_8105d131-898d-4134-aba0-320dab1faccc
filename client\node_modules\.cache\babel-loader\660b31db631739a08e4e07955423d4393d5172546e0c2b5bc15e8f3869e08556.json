{"ast": null, "code": "/**\n * lucide-react v0.125.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst WifiOff = createLucideIcon(\"WifiOff\", [[\"line\", {\n  x1: \"2\",\n  y1: \"2\",\n  x2: \"22\",\n  y2: \"22\",\n  key: \"1w4vcy\"\n}], [\"path\", {\n  d: \"M8.5 16.5a5 5 0 0 1 7 0\",\n  key: \"sej527\"\n}], [\"path\", {\n  d: \"M2 8.82a15 15 0 0 1 4.17-2.65\",\n  key: \"11utq1\"\n}], [\"path\", {\n  d: \"M10.66 5c4.01-.36 8.14.9 11.34 3.76\",\n  key: \"hxefdu\"\n}], [\"path\", {\n  d: \"M16.85 11.25a10 10 0 0 1 2.22 1.68\",\n  key: \"q734kn\"\n}], [\"path\", {\n  d: \"M5 13a10 10 0 0 1 5.24-2.76\",\n  key: \"piq4yl\"\n}], [\"line\", {\n  x1: \"12\",\n  y1: \"20\",\n  x2: \"12.01\",\n  y2: \"20\",\n  key: \"wbu7xg\"\n}]]);\nexport { WifiOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "x1", "y1", "x2", "y2", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\freelance-marketplace setup\\freelance-marketplace setup\\client\\node_modules\\lucide-react\\src\\icons\\wifi-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\nconst WifiOff = createLucideIcon('WifiOff', [\n  ['line', { x1: '2', y1: '2', x2: '22', y2: '22', key: '1w4vcy' }],\n  ['path', { d: 'M8.5 16.5a5 5 0 0 1 7 0', key: 'sej527' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 4.17-2.65', key: '11utq1' }],\n  ['path', { d: 'M10.66 5c4.01-.36 8.14.9 11.34 3.76', key: 'hxefdu' }],\n  ['path', { d: 'M16.85 11.25a10 10 0 0 1 2.22 1.68', key: 'q734kn' }],\n  ['path', { d: 'M5 13a10 10 0 0 1 5.24-2.76', key: 'piq4yl' }],\n  ['line', { x1: '12', y1: '20', x2: '12.01', y2: '20', key: 'wbu7xg' }],\n]);\n\nexport default WifiOff;\n"], "mappings": ";;;;;AAEM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,+BAAiC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,qCAAuC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}